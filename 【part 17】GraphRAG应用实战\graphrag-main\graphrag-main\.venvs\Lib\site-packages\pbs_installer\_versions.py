# @Generated by find_versions.py. DO NOT EDIT.
from __future__ import annotations
from ._utils import PythonVersion

PYTHON_VERSIONS: dict[<PERSON>Version, dict[tuple[str, str, bool], tuple[str, str | None]]] = {
    PythonVersion("cpython", 3, 13, 5, True): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-aarch64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "e6866eaa226227ff64ac0b200d6cda2c1efca3ef25c9950ee11725d22d73fff7",
        ),
        ("windows", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-aarch64-pc-windows-msvc-freethreaded%2Bpgo-full.tar.zst",
            "cc39b7f33e78c737071935d551660d795ac6a2e27101b61b527174b853f8119a",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-aarch64-unknown-linux-gnu-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "66c221e2f846e75a9191534a4228063047178ddbf808be5df25dbac6d823598a",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-i686-pc-windows-msvc-freethreaded%2Bpgo-full.tar.zst",
            "6dc326d094e9c1f35c5c30fcdf2f4d7e549ab958992969d556fbd8168b7e4ee3",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-x86_64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "606bcff8b3eadaa4d328b700ad2c3e4d44924b24eb833a85b23d7e425735fc1b",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-x86_64-pc-windows-msvc-freethreaded%2Bpgo-full.tar.zst",
            "6881d375ea3936f9d47fa6c36a7f8d3b53eac36e5dc78da110f646d8aaef86d0",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-x86_64-unknown-linux-gnu-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "f79ae7403bcfb36a1e7123707a4513e084f84a9e85a0e24186936a6e95e4f661",
        ),
    },
    PythonVersion("cpython", 3, 13, 5, False): {
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-aarch64-apple-darwin-install_only.tar.gz",
            "734c2f197cf2464d18308458c32679f38f22f31a911f86193e0f04dd56400f33",
        ),
        ("windows", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-aarch64-pc-windows-msvc-install_only.tar.gz",
            "4c2a578f472ec4bb34289b4afc5b930f97224b8cb351c9fa3722b8d736895ca3",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "a6f558daf7d4e0e7017ad827a4f6bcc9e8fc4be0e42ddbbb3d13540c7a58d829",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-i686-pc-windows-msvc-install_only.tar.gz",
            "6b88a3fb9db65244609818234e447812be524b096902c7225c47f83fee97a56c",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-x86_64-apple-darwin-install_only.tar.gz",
            "6b508822f5238451a5dcc52f07310b74aaa701ed963bba923cc7f4d24010cc21",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-x86_64-pc-windows-msvc-install_only.tar.gz",
            "191aaa9c725afad7570d24940268038823df7d14e0afbd25d1e23af0b99190a7",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.13.5%2B20250723-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "56bf8099cfcc3aac8dadcf2be53c48e5998d74cf5da600691dbf16be3f0b8f76",
        ),
    },
    PythonVersion("cpython", 3, 13, 4, True): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-aarch64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "278dccade56b4bbeecb9a613b77012cf5c1433a5e9b8ef99230d5e61f31d9e02",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-aarch64-unknown-linux-gnu-freethreaded%2Blto-full.tar.zst",
            "b1c1bd6ab9ef95b464d92a6a911cef1a8d9f0b0f6a192f694ef18ed15d882edf",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-i686-pc-windows-msvc-freethreaded%2Bpgo-full.tar.zst",
            "595786a6613306b74118c56e29ab430ed0eb08d82c4103b7778e827c5e43f8cf",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-x86_64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "64ab7ac8c88002d9ba20a92f72945bfa350268e944a7922500af75d20330574d",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-x86_64-pc-windows-msvc-freethreaded%2Bpgo-full.tar.zst",
            "9457504547edb2e0156bf76b53c7e4941c7f61c0eff9fd5f4d816d3df51c58e3",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-x86_64-unknown-linux-gnu-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "864df6e6819e8f8e855ce30f34410fdc5867d0616e904daeb9a40e5806e970d7",
        ),
    },
    PythonVersion("cpython", 3, 13, 4, False): {
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-aarch64-apple-darwin-install_only.tar.gz",
            "c2ce6601b2668c7bd1f799986af5ddfbff36e88795741864aba6e578cb02ed7f",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "3c2596ece08ffe17e11bc1f27aeb4ce1195d2490a83d695d36ef4933d5c5ca53",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-i686-pc-windows-msvc-install_only.tar.gz",
            "437829ee101e90381d8e5ef04d835dd5e316ae569d6ba1550014565e5587e7c1",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-x86_64-apple-darwin-install_only.tar.gz",
            "79feb6ca68f3921d07af52d9db06cf134e6f36916941ea850ab0bc20f5ff638b",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-x86_64-pc-windows-msvc-install_only.tar.gz",
            "29ac3585cc2dcfd79e3fe380c272d00e9d34351fc456e149403c86d3fea34057",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250610/cpython-3.13.4%2B20250610-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "44e5477333ebca298a7a0a316985c6c3533b8645f92a83f7f73c44033832bf32",
        ),
    },
    PythonVersion("cpython", 3, 13, 3, True): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-aarch64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "d1f00e1322f985daed5a812d7b212cb96f30dedf14575e7d5622d99f1c03ee95",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-aarch64-unknown-linux-gnu-freethreaded%2Blto-full.tar.zst",
            "d4d17846fa0d5bd617c74e7e7a0af40587f79fcf3b571362c97eee66da4d21ad",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-i686-pc-windows-msvc-freethreaded%2Bpgo-full.tar.zst",
            "005998b6503fe3b40cd16f040ca8bbb13947cc1336d37bafbe57ab1745f2fb54",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-x86_64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "7dcc77f8bfb8f958b3b375b30a72371b0aeda5e9973aba269f041eb093705c35",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-x86_64-pc-windows-msvc-freethreaded%2Bpgo-full.tar.zst",
            "c833faa78fe5349ed1b3e96fec14e9dba0930087eb303d478cff560207a2f7f7",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-x86_64-unknown-linux-gnu-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "9041b0fd8c4ede100c73afcb6c5a56febf8de6f2134d89740483c2da74e3c7e6",
        ),
    },
    PythonVersion("cpython", 3, 13, 3, False): {
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-aarch64-apple-darwin-install_only.tar.gz",
            "f6a8b64ded398d60b2a10dcef8d81df6d53a87baf24bee79d413cc6f35d3874d",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "ee001fa47bf5e2405713ba663ae681078394ae64ab04e1981acb927570a36b58",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-i686-pc-windows-msvc-install_only.tar.gz",
            "83fa6b2f06f117798e4898c05e5a174bc056f0f62cb93a889ba1b9ad221a63b8",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-x86_64-apple-darwin-install_only.tar.gz",
            "155ae058c171f2080fd0a17e66d01f2ce4a146e112a44f9b39dc497d88ec2909",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-x86_64-pc-windows-msvc-install_only.tar.gz",
            "a5513362690343663aeb51ae0c3989eb4e187ecdba0497aea6b72931472e245d",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.13.3%2B20250529-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "2a9fec6a0100d440c23c9d78f6d89a1a5b2a01e780abd7f5ec25ceb6ea0af985",
        ),
    },
    PythonVersion("cpython", 3, 13, 2, True): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.13.2%2B20250317-aarch64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "c98c9c977e6fa05c3813bd49f3553904d89d60fed27e2e36468da7afa1d6d5e2",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.13.2%2B20250317-aarch64-unknown-linux-gnu-freethreaded%2Blto-full.tar.zst",
            "b8635e59e3143fd17f19a3dfe8ccc246ee6587c87da359bd1bcab35eefbb5f19",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.13.2%2B20250317-x86_64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "0d73e4348d8d4b5159058609d2303705190405b485dd09ad05d870d7e0f36e0f",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.13.2%2B20250317-x86_64-unknown-linux-gnu-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "1aea5062614c036904b55c1cc2fb4b500b7f6f7a4cacc263f4888889d355eef8",
        ),
    },
    PythonVersion("cpython", 3, 13, 2, False): {
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.13.2%2B20250317-aarch64-apple-darwin-install_only.tar.gz",
            "faa44274a331eb39786362818b21b3a4e74514e8805000b20b0e55c590cecb94",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.13.2%2B20250317-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "9c67260446fee6ea706dad577a0b32936c63f449c25d66e4383d5846b2ab2e36",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250212/cpython-3.13.2%2B20250212-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "be51b28086459c042a0c8ef8b26e8d8bebba3ca16bfe6efa2c21419a35799c62",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.13.2%2B20250317-i686-pc-windows-msvc-install_only.tar.gz",
            "60b8effd1bd1badfdb5f275e89a43e032e9a46fd43b8e8f5c5b4894d50e6f19c",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.13.2%2B20250317-x86_64-apple-darwin-install_only.tar.gz",
            "ee4526e84b5ce5b11141c50060b385320f2773616249a741f90c96d460ce8e8f",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250212/cpython-3.13.2%2B20250212-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "db918659ecb75b0cbcd646da574e76df586c05ff73205876591c157357012358",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.13.2%2B20250317-x86_64-pc-windows-msvc-install_only.tar.gz",
            "84d7b52f3558c8e35c670a4fa14080c75e3ec584adfae49fec8b51008b75b21e",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.13.2%2B20250317-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "db011f0cd29cab2291584958f4e2eb001b0e6051848d89b38a2dc23c5c54e512",
        ),
    },
    PythonVersion("cpython", 3, 13, 1, True): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-aarch64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "730e23ac6dc7553222c7631772a3cf2ffca446ffa003df308281e75c25b6ec32",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-aarch64-unknown-linux-gnu-freethreaded%2Blto-full.tar.zst",
            "ac52451a26c7095f43d90a760337c2a87f69819476992d992fe867152408f46e",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-x86_64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "6af0320b6dfc9e7984e506d6d4677ebd864aab40379d64f2c0c3c21292e18b22",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-x86_64-unknown-linux-gnu-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "1c1cd64be626e63f5eadc6ef6f40a612741cb1d419f70a80d3c62898ebcc20d0",
        ),
    },
    PythonVersion("cpython", 3, 13, 1, False): {
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-aarch64-apple-darwin-install_only.tar.gz",
            "d00ac75b15a05da2f6bc0057fe36f6944f9d513239f1c7af12766e03f288fe65",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "df7f2a1ff4af907c16349b2a1554becfd3675b94289c7f978fd6e10faa6af4ab",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "c03eb477cf60aa80b182c93706410bf960908cf51b6a97fe899777ff27aab5c6",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-i686-pc-windows-msvc-install_only.tar.gz",
            "95528fd9bb3d37acfda58fcfc97ca6a7ad5fb66e45abe8dad0cd3dd32c75d6d6",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-x86_64-apple-darwin-install_only.tar.gz",
            "994c233cf86487b61283db63363ee969cf69dc551fba389c6d6d4e8534d4735f",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "38d26b0ae73d3c24c22daac820fb89212c4795dc85947d94952240621f6bdec6",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-x86_64-pc-windows-msvc-install_only.tar.gz",
            "92021e1151d118db94689a38407248de96d8a2b8ffa9c4ae31b990441fda0aa0",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.13.1%2B20250115-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "5e7d73b3144d51f15fca492c0f8afd9f690fd3e7bfa61e259beb3650d0336e1b",
        ),
    },
    PythonVersion("cpython", 3, 13, 0, True): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-aarch64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "efc2e71c0e05bc5bedb7a846e05f28dd26491b1744ded35ed82f8b49ccfa684b",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-aarch64-unknown-linux-gnu-freethreaded%2Blto-full.tar.zst",
            "59b50df9826475d24bb7eff781fa3949112b5e9c92adb29e96a09cdf1216d5bd",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-x86_64-apple-darwin-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "2e07dfea62fe2215738551a179c87dbed1cc79d1b3654f4d7559889a6d5ce4eb",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-x86_64-unknown-linux-gnu-freethreaded%2Bpgo%2Blto-full.tar.zst",
            "a73adeda301ad843cce05f31a2d3e76222b656984535a7b87696a24a098b216c",
        ),
    },
    PythonVersion("cpython", 3, 13, 0, False): {
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-aarch64-apple-darwin-install_only.tar.gz",
            "31397953849d275aa2506580f3fa1cb5a85b6a3d392e495f8030e8b6412f5556",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "e8378c0162b2e0e4cc1f62b29443a3305d116d09583304dbb0149fecaff6347b",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "0ef249cb7ba1e2c451e0a551df063f9caf2e2dc37e09d89f780f98991cd433db",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-i686-pc-windows-msvc-install_only.tar.gz",
            "97e2b0c3d177946a8f70bbe2d993659f8c1e727dfe52ebc5f9f33dca62af8209",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-x86_64-apple-darwin-install_only.tar.gz",
            "cff1b7e7cd26f2d47acac1ad6590e27d29829776f77e8afa067e9419f2f6ce77",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "4b4940208e6c0475105fd2c8bfc2e29a4ccdb61350b26ef13172303930a23b4e",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-x86_64-pc-windows-msvc-install_only.tar.gz",
            "b25926e8ce4164cf103bacc4f4d154894ea53e07dd3fdd5ebb16fb1a82a7b1a0",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.13.0%2B20241016-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "2c8cb15c6a2caadaa98af51df6fe78a8155b8471cb3dd7b9836038e0d3657fb4",
        ),
    },
    PythonVersion("cpython", 3, 12, 11, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "03f26b105443c70e594c2eebf2439a07d04a23543bf1016ac819b6a021e501a2",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-aarch64-apple-darwin-install_only.tar.gz",
            "141272e6c6ae945b61fcf4073b7419451f8227187b3667b01ea9ec8993e0d7e9",
        ),
        ("windows", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-aarch64-pc-windows-msvc-install_only.tar.gz",
            "3a7965165a8eabd1eebdbe6427ef9d2580fe41cdac5eb0930527ddea280f5414",
        ),
        ("windows", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-aarch64-pc-windows-msvc-pgo-full.tar.zst",
            "df5799323ffaaa710e7e780a0f3d873d0471ef2ddc5826b4a96a8c8af9406c09",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-aarch64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "cf9010cae4a4d70231635608be17e91c8afe367c161582e997415ecd5048b2da",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "6344f857fab8453423cf0471a9bd1e74393947c9e3d59919d78eebc4c7eb8590",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-i686-pc-windows-msvc-install_only.tar.gz",
            "040732834d4008ee5faa0684b8dbe56b83436c510b2e2d887e2fbdd7c53b24a4",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-i686-pc-windows-msvc-pgo-full.tar.zst",
            "a2edf0829b623373591a93626c60c37c83871f0581a7b20fef6c483c604841d9",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "b56c3afcc6da4267b7856c96fcac712621a086c74cb33db01ec4776fe5f35446",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-x86_64-apple-darwin-install_only.tar.gz",
            "1f152ee0dcc6ac5db93e39d74f0c50e319863d65fea0aab04e2e1b3f49b87f5f",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-x86_64-pc-windows-msvc-install_only.tar.gz",
            "14be5b19f1004d65fb1e9fe9152a857af7d36c7f471dd5e2c10bf7f58f6c1838",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-x86_64-pc-windows-msvc-pgo-full.tar.zst",
            "2f5a309b2e028f9d011e9942926ba7826bbed0413d1a6116e005b30674588aaf",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "adfc2112a758e446858891b425084cbddcf21085a1847dd0f5ec6077c781d3ef",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.12.11%2B20250723-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "e5e828ba2c1a55400cdb07ff5f052908c46d5deb4f62d8b5825f8c24aeffcccb",
        ),
    },
    PythonVersion("cpython", 3, 12, 10, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "7b3698a64577aae9b6c32f31891dc367618714e0df3393fd7f1bb0d351c99200",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-aarch64-apple-darwin-install_only.tar.gz",
            "896af337e29993b52fecea36e5db371e6569affb7ef5de9ebb166045a0b158eb",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "dd002b54c2bd802b6733fad6169b18ff0eabab60f2d4128754239411f0aca6f4",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "3a3b056810dfeab40e2b9ec91db1b928707e2a6ac4457a8be8a8b82eccb49070",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-i686-pc-windows-msvc-install_only_stripped.tar.gz",
            "17d998bc06ca212b99fd6c53ac8d790c5d6682778165cf351f304ee9f00143fa",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-i686-pc-windows-msvc-pgo-full.tar.zst",
            "1d7690a1c295be9a627c1bd2072e64a81038bb9822fdccbcdee1cc4cb624f04a",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "5457d043687efd608ce68d6b4599295af206f1e09fd2fdced9d9cfeb57033014",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-x86_64-pc-windows-msvc-install_only.tar.gz",
            "3e0fd10e4bd60e66baad8be2a0e60775bb389e97e4f4a07138c34599c356aaed",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "3b7de567f4a8a1c3e5be2d51af3023dd3c1075cfacb6cd8efb511e0ef4b31fa8",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "9ea191b9620e1e3d6f81ec0373d4d5006d4d1c482247a4824f7f69f2212016a8",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-x86_64-apple-darwin-install_only_stripped.tar.gz",
            "f171af578ecdbbcf63cbb0abd08f382df3e0d091e98c3161b3f84cd9d0acc0fd",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.12.10%2B20250529-x86_64-pc-windows-msvc-pgo-full.tar.zst",
            "270c705750c605eb37c2f235e6bfca11f89beac3194d0f6822824712389d99c1",
        ),
    },
    PythonVersion("cpython", 3, 12, 9, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.12.9%2B20250317-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "3c151d4c5e3c3e4353ce1f6653c38303d807f5653306b4f3d72c188314b69bba",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.12.9%2B20250317-aarch64-apple-darwin-install_only.tar.gz",
            "7c7fd9809da0382a601a79287b5d62d61ce0b15f5a5ee836233727a516e85381",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.12.9%2B20250317-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "569618dc4cfca71c59e687f550b1bdef3600b34eb1d7d9e8e5e77fd1c3f14107",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.12.9%2B20250317-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "00c6bf9acef21ac741fea24dc449d0149834d30e9113429e50a95cce4b00bb80",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.12.9%2B20250317-i686-pc-windows-msvc-install_only.tar.gz",
            "f8e24119bad955f1b55482d58065a35e0c32d992f0025e888356e1e3b4b94500",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250212/cpython-3.12.9%2B20250212-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "5de64b14321a60b143bed087462442bdbc79caccf75c644c156d031abe4ea190",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.12.9%2B20250317-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "78d04aebe078345adc18ba8b6fd69ee30e48cece6715064ecb3e65e5a8bfc710",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.12.9%2B20250317-x86_64-apple-darwin-install_only.tar.gz",
            "1ee1b1bb9fbce5c145c4bec9a3c98d7a4fa22543e09a7c1d932bc8599283c2dc",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.12.9%2B20250317-x86_64-pc-windows-msvc-install_only.tar.gz",
            "d15361fd202dd74ae9c3eece1abdab7655f1eba90bf6255cad1d7c53d463ed4d",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250212/cpython-3.12.9%2B20250212-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "ef8b7988f2cb5490ebd795af49a34e66d45510c9682fe7938070c697769e3bdc",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.12.9%2B20250317-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "fff64711a7734d67045441ace24baf1935c87be5e288b0543bfe40e9af5507cc",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.12.9%2B20250317-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "ef382fb88cbb41a3b0801690bd716b8a1aec07a6c6471010bcc6bd14cd575226",
        ),
    },
    PythonVersion("cpython", 3, 12, 8, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "768afd34fd5aa893e741d692b817d71940a75ec9b66935ec6eea1046c831f7a6",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-aarch64-apple-darwin-install_only.tar.gz",
            "e29003b69465c33692830032d9d237d84ea43a2e8461db9134641640fb49f040",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "113ae4c04c95a5282025a7c27f6fc3a98b48ecb7e36881b6170926330f5c5f2e",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "2e08c1d4de239290b9fc3bef90f121349819b473149083470d16081dd293050c",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-i686-pc-windows-msvc-install_only.tar.gz",
            "f2755c84675e75ef73f7066b1f3f6768a4cb9a43a928ab55bdbb4d6a8a570655",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "25100a4088d5634551295e50c4dbfedb28bec2f0b3c83bb833195f33d3f11a0e",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "464c6cbabf6e45795d25b39287ebd14fd4a46157e03358dc2c4380e7bb8fbf3f",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-x86_64-apple-darwin-install_only.tar.gz",
            "b81ae8ea17fce6e173649120fcc4eda123bb8df54890894bbec432f527fbe75c",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-x86_64-pc-windows-msvc-install_only.tar.gz",
            "86ee8267900240c96369adb2cbc1af8f543f860d2e22be5adb7362f3cbe61059",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "5a7ddf02225e3b6df406bbf353e3b0a62dcd39dfef512c24197fcbff88a075b9",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "fb3dda384590b22514b5f8103ed2d9c0591dd89c8ab8138b5eb6f3169df70dcd",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250115/cpython-3.12.8%2B20250115-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "e5435e717c934ed30d4066f64e858497c27f37c1ba547f403b050d9221e50ea4",
        ),
    },
    PythonVersion("cpython", 3, 12, 7, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "3f8bf335c97424d0be262687906b0f50511f22fede8b9be77363faa57c4b8e7f",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-aarch64-apple-darwin-install_only.tar.gz",
            "4c18852bf9c1a11b56f21bcf0df1946f7e98ee43e9e4c0c5374b2b3765cf9508",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "bd7688441ddf8d5dc2ccb288bb31fb862330999a4a3f1544aa4f999c93b85a6a",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "bba3c6be6153f715f2941da34f3a6a69c2d0035c9c5396bc5bb68c6d2bd1065a",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-i686-pc-windows-msvc-install_only.tar.gz",
            "f14bad9d72451bcc1bb0cd033951ad1bf482511570ea3ab3bda4d954313bfdfc",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "199826ee08e52c9476a2376b43bb712da6b6464b26d56b0a1c165fddb3733770",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "4ae54498bd8d17fc8689fc57c7dadd59c625834af1b5f81fa21f943ff0e37643",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-x86_64-apple-darwin-install_only.tar.gz",
            "60c5271e7edc3c2ab47440b7abf4ed50fbc693880b474f74f05768f5b657045a",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-x86_64-pc-windows-msvc-install_only.tar.gz",
            "f05531bff16fa77b53be0776587b97b466070e768e6d5920894de988bdcd547a",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "bb0029bbe40c7c0851734765db1747589ff2ea85d87297c4e2d968e397c601e2",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "a3709fb5350bff838bd7bb5dc18e67300c76dc3e5cd11f9c84e54c8aeac60784",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.12.7%2B20241016-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "43576f7db1033dd57b900307f09c2e86f371152ac8a2607133afa51cbfc36064",
        ),
    },
    PythonVersion("cpython", 3, 12, 6, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "b9b115e897e534bd6c1a4f4949fa3c75d662218c3c94bb47f87e61f8c6df430a",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-aarch64-apple-darwin-install_only.tar.gz",
            "899f46eb592fcac4e834c064e4c901e8a4a6b5864e80b18efd2f0b7c3c050584",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "93a238ba9707c357c054d665a940e2ef33c7c2e2d4e454c248941e625c3050d9",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "caac1033f68f69d8978dc8c6b6964cfb9d8a111abc55c03403bd4ece63f331f3",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-i686-pc-windows-msvc-install_only.tar.gz",
            "b278a0100007df0c7ef21fbb3b7e06c9268e6e4a89b719b021fd595c24956b0e",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "8e83f98c4b0f83a9ef4c8f90877513feb0bb7f0c2f2c6bc63077511d67e7b3ab",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "e38c3f031ebfd898f16f10bc73655f377787624f4915113f48d5f017ced0a9de",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-x86_64-apple-darwin-install_only.tar.gz",
            "8c56da91436bee158b0d592aed3393c1fe3da3694ca35950ee1c52935ba8bfd5",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-x86_64-pc-windows-msvc-install_only.tar.gz",
            "6280ce84c87ebaca2c4b42040bad48e7efbfd1b3f323579378ecf043e9fb023d",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "7566acba13d60fe059263c906a67f2450d7ae3d5749e524ffb21baa68e924cd3",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "5b560c74201a5fc1d6771cdc12957b4b2f792dea76134b4d060178690c683d04",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240909/cpython-3.12.6%2B20240909-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "68ff386c923c59a33a272bd984b8a33fe8117c56ad7f7552e0c2b21937ee3c0b",
        ),
    },
    PythonVersion("cpython", 3, 12, 5, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "40b7a9bddca90217102e07f5bc2747c75534a1cced299d176a9c0901251a691b",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-aarch64-apple-darwin-install_only.tar.gz",
            "6943873ffcede238280a8fc0dbd4916c9bff54cf6a759352f86077c556c0c3a5",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "bf5b434987f3eb7fb65111e7dbf24d82e961ef4e95400e54721035c0904b42c8",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "36181612a3467f5e7c322c69fa85e12baf8370ae33456fca7cc821cfbe4df5f7",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-i686-pc-windows-msvc-install_only.tar.gz",
            "9c2059efefcabd8d391cc0fdcffdc6214808f62a9d673c4457a6640329e0973b",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "4b0f8e4bcd280fb595c2bbba8d1ae37831c989d2b301b5cefe9fba5e51b506b6",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "0556dccef4c94637d6f4f7f645608b72db0a64c43c3a59cf0d9ec021ddf75a30",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-x86_64-apple-darwin-install_only.tar.gz",
            "4c7619c25c037d377eebe8c7b98e6c818276b55714536ea82be2325d5e8ad572",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-x86_64-pc-windows-msvc-install_only.tar.gz",
            "ba89687d1a1b68e662ca40bdbcbfb2457903553329ba3020f4c96365fda4a254",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "a7ddc238799f15c25ac6d4ff7bec3464d9fa8b5e8cf13dad434d89afd0697a1d",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "e4c7b70f1c8b8ff062f567e048777f55cc9d2a98dd6b71abaf8b10a0e1670906",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.12.5%2B20240814-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "3be3bd9b7bd11f4a71bcaf16813fe61f93812cdb814da3fb0d7298f1086752ef",
        ),
    },
    PythonVersion("cpython", 3, 12, 4, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "3b017ab70e2f11b95d909317fc4e76c000ece588461058a642bf74db77cec267",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-aarch64-apple-darwin-install_only.tar.gz",
            "1801025e825c04b3907e4ef6220a13607bc0397628c9485897073110ef7fde15",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "05cbc91569f94a07f96a7ae04b62e19a9d7a3e74400d7d6b5b042f17285d2601",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "a098b18b7e9fea0c66867b76c0124fce9465765017572b2e7b522154c87c78d7",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-i686-pc-windows-msvc-install_only.tar.gz",
            "b66593869efa4e15412740681135a61956fe73a63fe6c37ce26d35c9fd008576",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "4d321e97e499610c887da89682c796967dba9337a1102e9e5d98d65cef5dde52",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "60cfc2465326cb44303e3a8904d4e606ec54aaa41be378532e219f05f06ef37d",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-x86_64-apple-darwin-install_only.tar.gz",
            "4c325838c1b0ed13698506fcd515be25c73dcbe195f8522cf98f9148a97601ed",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-x86_64-pc-windows-msvc-install_only.tar.gz",
            "74309b0f322716409883d38c621743ea7fa0376eb00927b8ee1e1671d3aff450",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "369d3a8a205cdbc754034f304ecedd4da0120cc9c71b6baac0147908aba15ece",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "164f9ca029de9220d6f473e835b06c14684905912aee73312c560238fc2051d6",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240726/cpython-3.12.4%2B20240726-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "e133dd6fc6a2d0033e2658637cc22e9c95f9d7073b80115037ee1f16417a54ac",
        ),
    },
    PythonVersion("cpython", 3, 12, 3, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "fa2b8c377f17dfb097a93c0fba217d93075a7ceba0cc877066e95be969e6b73d",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-aarch64-apple-darwin-install_only.tar.gz",
            "ccc40e5af329ef2af81350db2a88bbd6c17b56676e82d62048c15d548401519e",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "a4f17d1e3b4ea0e4c2a3664f232c0857979522936af582f7de92b57050220f74",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "ec8126de97945e629cca9aedc80a29c4ae2992c9d69f2655e27ae73906ba187d",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-i686-pc-windows-msvc-install_only.tar.gz",
            "bd723ad1aa05551627715a428660250f0e74db0f1421b03f399235772057ef55",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "31bb3f579f3dcbbf3bf1dc71a188112e821cdfc77d21c9dbfe82ea78538110e1",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "e49da3f702da08a3e38d01c776cc2356e427217681964ff64a7880507e224a3c",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-x86_64-apple-darwin-install_only.tar.gz",
            "c37a22fca8f57d4471e3708de6d13097668c5f160067f264bb2b18f524c890c8",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-x86_64-pc-windows-msvc-install_only.tar.gz",
            "f7cfa4ad072feb4578c8afca5ba9a54ad591d665a441dd0d63aa366edbe19279",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "776568c92c5f3b47dbf5f17c1c58578f70d75a32654419a158aa8bdc6f95b09a",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "e51f6676a24c3551657347ef97963164eac801df0a62afcba8e0e28ebb62acee",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240415/cpython-3.12.3%2B20240415-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "a73ba777b5d55ca89edef709e6b8521e3f3d4289581f174c8699adfb608d09d6",
        ),
    },
    PythonVersion("cpython", 3, 12, 2, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "2afcc8b25c55793f6ceb0bef2e547e101f53c9e25a0fe0332320e5381a1f0fdb",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-aarch64-apple-darwin-install_only.tar.gz",
            "01c064c00013b0175c7858b159989819ead53f4746d40580b5b0b35b6e80fba6",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "2e87c0215aea1614e52ff8588b0ba41eb5ecf555e500094a179c0bbf1b25cbc7",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "e52550379e7c4ac27a87de832d172658bc04150e4e27d4e858e6d8cbb96fd709",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "1e919365f3e04eb111283f7a45d32eac2f327287ab7bf46720d5629e144cbff9",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "ee985ae6a6a98f4d5bd19fd8c59f45235911d19b64e1dbd026261b8103f15db5",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "b4b4d19c36e86803aa0b4410395f5568bef28d82666efba926e44dbe06345a12",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-x86_64-apple-darwin-install_only.tar.gz",
            "a53a6670a202c96fec0b8c55ccc780ea3af5307eb89268d5b41a9775b109c094",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "1e5655a6ccb1a64a78460e4e3ee21036c70246800f176a6c91043a3fe3654a3b",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "a1daf5e8ceb23d34ea29b16b5123b06694810fe7acc5c8384426435c63bf731e",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "67065f1215e4274edbc44fa368d7d64525a2601636842cff880c2ea538279e0c",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.12.2%2B20240224-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "57a37b57f8243caa4cdac016176189573ad7620f0b6da5941c5e40660f9468ab",
        ),
    },
    PythonVersion("cpython", 3, 12, 1, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "61e51e3490537b800fcefad718157cf775de41044e95aa538b63ab599f66f3a9",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-aarch64-apple-darwin-install_only.tar.gz",
            "f93f8375ca6ac0a35d58ff007043cbd3a88d9609113f1cb59cf7c8d215f064af",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "3621be2cd8b5686e10a022f04869911cad9197a3ef77b30879fe25e792d7c249",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "236533ef20e665007a111c2f36efb59c87ae195ad7dca223b6dc03fb07064f0b",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "13c8a6f337a4e1ef043ffb8ea3c218ab2073afe0d3be36fcdf8ceb6f757210e8",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "22866d35fdf58e90e75d6ba9aa78c288b452ea7041fa9bc5549eca9daa431883",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "bf2b176b0426d7b4d4909c1b19bbb25b4893f9ebdc61e32df144df2b10dcc800",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-x86_64-apple-darwin-install_only.tar.gz",
            "eca96158c1568dedd9a0b3425375637a83764d1fa74446438293089a8bfac1f8",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "fd5a9e0f41959d0341246d3643f2b8794f638adc0cec8dd5e1b6465198eae08a",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "d9bc1b566250bf51818976bf98bf50e1f4c59b2503b50d29250cac5ab5ef6b38",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "f267489a041daf4e523c03d32639de04ee59ca925dff49a8c3ce2f28a9f70a3b",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.12.1%2B20240107-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "74e330b8212ca22fd4d9a2003b9eec14892155566738febc8e5e572f267b9472",
        ),
    },
    PythonVersion("cpython", 3, 12, 0, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "25fc8cd41e975d18d13bcc8f8beffa096ff8a0b86c4a737e1c6617900092c966",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-aarch64-apple-darwin-install_only.tar.gz",
            "4734a2be2becb813830112c780c9879ac3aff111a0b0cd590e65ec7465774d02",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "86e16b6defbbd7db0b7f98879b2b381e0e5b0ec07126cb9f5fc0cafe9869dc36",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "bccfe67cf5465a3dfb0336f053966e2613a9bc85a6588c2fcf1366ef930c4f88",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "6e4f30a998245cfaef00d1b87f8fd5f6c250bd222f933f8f38f124d4f03227f9",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "465e91b6e6d0d1c40c8a4bce3642c4adcb9b75cf03fbd5fd5a33a36358249289",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "3b4781e7fd4efabe574ba0954e54c35c7d5ac4dc5b2990b40796c1c6aec67d79",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-x86_64-apple-darwin-install_only.tar.gz",
            "5a9e88c8aa52b609d556777b52ebde464ae4b4f77e4aac4eb693af57395c9abf",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "facfaa1fbc8653f95057f3c4a0f8aa833dab0e0b316e24ee8686bc761d4b4f8d",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "5bdff7ed56550d96f9b26a27a8c25f0cc58a03bff19e5f52bba84366183cab8b",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "5ce861907a2751a3a7395b1aaada830c2b072acc03f3dd0bcbaaa2b7a9166fc0",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.12.0%2B20231002-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "e51a5293f214053ddb4645b2c9f84542e2ef86870b8655704367bd4b29d39fe9",
        ),
    },
    PythonVersion("cpython", 3, 11, 13, False): {
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-aarch64-apple-darwin-install_only.tar.gz",
            "2e68e228469a2f006a4f68e07e17ce251338ccdb848340152f3473d0eb158489",
        ),
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "40f004882e5bb7ec5796120aee869d7ec05e9302c4b322482a7815c6b1580e12",
        ),
        ("windows", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-aarch64-pc-windows-msvc-install_only.tar.gz",
            "db65e52576dc50344c3f78045dce0d650a1bfe003ba802ae44ad0c2b6b75faa1",
        ),
        ("windows", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-aarch64-pc-windows-msvc-pgo-full.tar.zst",
            "139140df3a3158d1aa9ceb9e509e9b58942c505d420b358e7db89d530113210f",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-aarch64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "6b65a93bf05ca9c54ace6e08f048a0c3e1d31a6d6336372b6167bcc2f776dfe5",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "8d1addbd90e9a7c48fc23151834c4af4c64f77a94e9a35ca17b09d484d9208af",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-i686-pc-windows-msvc-install_only.tar.gz",
            "baa79ec704733c935ca75182f2c54709fd5b8eaaa1fb1c65e13f7b970aba5bd1",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-i686-pc-windows-msvc-pgo-full.tar.zst",
            "8691e9417497ae05fc06c7d886898570a33afcdeef722e84077bc2194018194f",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "f826f6a697516df1c1ef101e61c40f8117d9def0f93039eb16e4c795592ea003",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-x86_64-apple-darwin-install_only.tar.gz",
            "c27ec8d445a806496d9204d84e781d0ef81d7abbad4a49676a38b140f7a0515a",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-x86_64-pc-windows-msvc-install_only.tar.gz",
            "d6b1931efac5b75daca11563f88f22a3791437c6424f0300cc2c37ad78512e1c",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-x86_64-pc-windows-msvc-pgo-full.tar.zst",
            "f2b09b3c96636d1e3e23a9be8f0db8cd791cb242c30c494aede2e5697838c47e",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "8cc2a9d3e4e7365a4378a7927e6cddf9ab4ceb55773f22da252d454b3ad626e6",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.11.13%2B20250723-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "71c0eb7f5025e2cde7a473b724e2959041fdba243ea58bea71ea2e3455107ea2",
        ),
    },
    PythonVersion("cpython", 3, 11, 12, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "0be7749492272981fd9cb4bfc668e88f0ed649c45a39e4b7d87349fb3a97fd1e",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-aarch64-apple-darwin-install_only.tar.gz",
            "77d16e24444fa12096818064fcc3c12b041b0746f4481e3652fc60ee027a7fb5",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "37f2af0993a48098d55e5c174b8e66bbdeae1cb924140e2884b0bb111c57596b",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "099085938a916e36c830ad2d74c78d760757d7f3218906394d49fff20c34b4d0",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-i686-pc-windows-msvc-install_only.tar.gz",
            "1ff406567d68876798d1bb1c481b57611b3b5cecb64645b425ee5e065bcf2fdc",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-i686-pc-windows-msvc-pgo-full.tar.zst",
            "c6ca18c3024b276db8803c267ddaa413f2f29f4980ddf4b6c12439b1c97e9ba0",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "656164ed94b26c95b6f4806ad8a98898fa9b5e8c5523bea19f3370cefdca5f42",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-x86_64-apple-darwin-install_only.tar.gz",
            "1fc7ee75b37a309443d5a214b83733cfda5ae7597559fb39ab8906f09c997c93",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-x86_64-pc-windows-msvc-install_only.tar.gz",
            "3258b902130179f72a3086ad87deccfa2f111faff54de444535d7b72d99f2b20",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "834f469dfc49bf37493392368c721be0b034e44219bb777b1e44a1603ca1ed43",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-x86_64-pc-windows-msvc-pgo-full.tar.zst",
            "803ba12533e3ff9c980dadace42747c7127db3641163c11e677004af0398cefc",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.11.12%2B20250529-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "0c05bfc1e1f0f0b084af9e8b19f778cad6b431201a3169587291a6c19264eb81",
        ),
    },
    PythonVersion("cpython", 3, 11, 11, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11%2B20250317-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "8709617529e3de5b985fa7ad191426f7647679116ede8e1676e1a1ea15482905",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11%2B20250317-aarch64-apple-darwin-install_only.tar.gz",
            "19b147c7e4b742656da4cb6ba35bc3ea2f15aa5f4d1bbbc38d09e2e85551e927",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11%2B20250317-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "516f3c7db3e17bd52e4f7a2eaa125c15bb5b3558a63a52f8a1e0875b4852a2f9",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11%2B20250317-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "7d52b5206afe617de2899af477f5a1d275ecbce80fb8300301b254ebf1da5a90",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11%2B20250317-i686-pc-windows-msvc-install_only.tar.gz",
            "b87318a6d7f04753c6687a3df86a33fc4a879ecd6acbfac0c23d31ea42421567",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250212/cpython-3.11.11%2B20250212-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "7a0a9c92cc2fc088e232a4cd5ce7572da8b05b4cb9bef950244c7902a598f4e7",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11%2B20250317-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "ce4e5127f5ad9ea45f1e86db680040852d74af409abb70438219aae1ffd0a948",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11%2B20250317-x86_64-apple-darwin-install_only.tar.gz",
            "a870cd965e7dded5100d13b1d34cab1c32a92811e000d10fbfe9bbdb36cdaa0e",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11%2B20250317-x86_64-pc-windows-msvc-install_only.tar.gz",
            "1cf5760eea0a9df3308ca2c4111b5cc18fd638b2a912dbe07606193e3f9aa123",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250212/cpython-3.11.11%2B20250212-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "6ae7c6cb4f42f2c2d554bfe9a4ad1900acd6f9cf0c1125f4e4812489a2d82722",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11%2B20250317-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "b1def64f585023257b9b6a8ce8bf47b0ac4232bebbec3fac3ef489a2fd4b1197",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11%2B20250317-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "51e47bc0d1b9f4bf68dd395f7a39f60c58a87cde854cab47264a859eb666bb69",
        ),
    },
    PythonVersion("cpython", 3, 11, 10, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "afac902c6498cda4095674344b6defbff4500a3687aea597b3710cd070b62756",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-aarch64-apple-darwin-install_only.tar.gz",
            "5a69382da99c4620690643517ca1f1f53772331b347e75f536088c42a4cf6620",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "4aa77466ca61b0a3263e3bf55be911cc72f30fd33c81d545f13782b1a35daeec",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "803e49259280af0f5466d32829cd9d65a302b0226e424b3f0b261f9daf6aee8f",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-i686-pc-windows-msvc-install_only.tar.gz",
            "68a1fc11c8be1cae82278f9eef6fc4c1c5d48e3a81081339043671d284f6d83c",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "b802510e566aa7c58f6368ffde2b14fa7bd5147fc221e253c90253c2e88c6119",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "672a3d428359c3edd2155bf9d9939e0f51803fe3c81f1643e021d9970f01fbdd",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-x86_64-apple-darwin-install_only.tar.gz",
            "1e23ffe5bc473e1323ab8f51464da62d77399afb423babf67f8e13c82b69c674",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-x86_64-pc-windows-msvc-install_only.tar.gz",
            "647b66ff4552e70aec3bf634dd470891b4a2b291e8e8715b3bdb162f577d4c55",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "11daa1c645cb8e76f513adc5998ab462daa6f1d939ef39710d5ca34f69648812",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "36498e63788f5a03c0d6249a5df77fbca542bf066eaa6dbb8bbf516194f96e9b",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.11.10%2B20241016-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "8b50a442b04724a24c1eebb65a36a0c0e833d35374dbdf9c9470d8a97b164cd9",
        ),
    },
    PythonVersion("cpython", 3, 11, 9, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "6436b83fed284af320743f6f5148ab3decbdc054531b40848977a5fa2347ca44",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-aarch64-apple-darwin-install_only.tar.gz",
            "8760e908f25fdc8a01f4d1b101854ac047b4eacb723fb2593a168fb989c86eef",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "63cd27b797589b66689c5be6495c707311d8272b284ad20faff1814b00134ac7",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "c74a3313e081dda8360d1824baa4eae5303333555bf8440ec2de521731e39165",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-i686-pc-windows-msvc-install_only.tar.gz",
            "08e3e397520a00113a9e009d87a751041e6cfbeae598f1eca4bd7a068fe26e30",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "88e3238b59aad1b624f0c45c058059e5c582e686563e3993b1b1dadddfa3fe1d",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "2caa7756679ec65a795b99e306de00ea0a4069bd7b1d6ec45df89d6e37a29577",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-x86_64-apple-darwin-install_only.tar.gz",
            "76073305812c093ce840df9c4c17068aa69da8d951e7376ef48f43376986a13e",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-x86_64-pc-windows-msvc-install_only.tar.gz",
            "4c71d25731214b8a960d1d87510f24179d819249c5b434aaf7135818421b6215",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "7766550c42ce59d53b1dd49e9d698d762c9e5a743c7a57d6d7114ff7d266e131",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "73b3bef1220efcfd61dec42af94b9792937fe388bcc7064017c8f3b8e4636187",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.11.9%2B20240814-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "9a332ba354f3b4e8a96a15db6b2805a7a31dcc1b6b9c1b7b93e5246949fbb50f",
        ),
    },
    PythonVersion("cpython", 3, 11, 8, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "c0650884b929253b8688797d1955850f6e339bf0428b3d935f62ab3159f66362",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-aarch64-apple-darwin-install_only.tar.gz",
            "389a51139f5abe071a0d70091ca5df3e7a3dfcfcbe3e0ba6ad85fb4c5638421e",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "1d84ed69e5acce555513e9261ce4b78bed19969b06a51a26b2781a375d70083d",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "389b9005fb78dd5a6f68df5ea45ab7b30d9a4b3222af96999e94fd20d4ad0c6a",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "75039951f8f94d7304bc17b674af1668b9e1ea6d6c9ba1da28e90c0ad8030e3c",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "c3e90962996177a027bd73dd9fd8c42a2d6ef832cda26db4ab4efc6105160537",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "54f8c8ad7313b3505e495bb093825d85eab244306ca4278836a2c7b5b74fb053",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-x86_64-apple-darwin-install_only.tar.gz",
            "097f467b0c36706bfec13f199a2eaf924e668f70c6e2bd1f1366806962f7e86e",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "b618f1f047349770ee1ef11d1b05899840abd53884b820fd25c7dfe2ec1664d4",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "6da82390f7ac49f6c4b19a5b8019c4ddc1eef2c5ad6a2f2d32773a27663a4e14",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "ae1bf11b438304622d9334092491266f908f26d76da03f1125514a192cf093f8",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.11.8%2B20240224-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "94e13d0e5ad417035b80580f3e893a72e094b0900d5d64e7e34ab08e95439987",
        ),
    },
    PythonVersion("cpython", 3, 11, 7, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "c1f3dd13825906a5eae23ed8de9b653edb620568b2e0226eef3784eb1cce7eed",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-aarch64-apple-darwin-install_only.tar.gz",
            "b042c966920cf8465385ca3522986b12d745151a72c060991088977ca36d3883",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "e066d3fb69162e401d2bb1f3c20798fde7c2fffcba0912d792e46d569b591ab3",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "b102eaf865eb715aa98a8a2ef19037b6cc3ae7dfd4a632802650f29de635aa13",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "f5a6ca1280749d8ceaf8851585ef6b0cd2f1f76e801a77c1d744019554eef2f0",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "6613f1f9238d19969d8a2827deec84611cb772503207056cc9f0deb89bea48cd",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "3f8caf73f2bfe22efa9666974c119727e163716e88af8ed3caa1e0ae5493de61",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-x86_64-apple-darwin-install_only.tar.gz",
            "a0e615eef1fafdc742da0008425a9030b7ea68a4ae4e73ac557ef27b112836d4",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "67077e6fa918e4f4fd60ba169820b00be7c390c497bf9bc9cab2c255ea8e6f3e",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "89d1d8f080e5494ea57918fc5ecf3d483ffef943cd5a336e64da150cd44b4aa0",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "b7e19b262c19dfb82107e092ba3959b2da9b8bc53aafeb86727996afdb577221",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240107/cpython-3.11.7%2B20240107-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "4a51ce60007a6facf64e5495f4cf322e311ba9f39a8cd3f3e4c026eae488e140",
        ),
    },
    PythonVersion("cpython", 3, 11, 6, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "6e9007bcbbf51203e89c34a87ed42561630a35bc4eb04a565c92ba7159fe5826",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-aarch64-apple-darwin-install_only.tar.gz",
            "916c35125b5d8323a21526d7a9154ca626453f63d0878e95b9f613a95006c990",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "7c621a748a4fd6ae99d8ba7ec2da59173d31475838382a13df6d2b1bf95a7059",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "3e26a672df17708c4dc928475a5974c3fb3a34a9b45c65fb4bd1e50504cc84ec",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "dd48b2cfaae841b4cd9beed23e2ae68b13527a065ef3d271d228735769c4e64d",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "2670731428191d4476bf260c8144ccf06f9e5f8ac6f2de1dc444ca96ab627082",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "3685156e4139e89484c071ba1a1b85be0b4e302a786de5a170d3b0713863c2e8",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-x86_64-apple-darwin-install_only.tar.gz",
            "178cb1716c2abc25cb56ae915096c1a083e60abeba57af001996e8bc6ce1a371",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "3933545e6d41462dd6a47e44133ea40995bc6efeed8c2e4cbdf1a699303e95ea",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "38d2c2fa2f9effbf486207bef7141d1b5c385ad30729ab0c976e6a852a2a9401",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "6da291720c9fe2f63c5c55f7acc8b6094a05488453a84cfcc012e92305099ee7",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20231002/cpython-3.11.6%2B20231002-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "ee37a7eae6e80148c7e3abc56e48a397c1664f044920463ad0df0fc706eacea8",
        ),
    },
    PythonVersion("cpython", 3, 11, 5, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "7bee180b764722a73c2599fbe2c3a6121cf6bbcb08cb3082851e93c43fe130e7",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-aarch64-apple-darwin-install_only.tar.gz",
            "dab64b3580118ad2073babd7c29fd2053b616479df5c107d31fe2af1f45e948b",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "cf131546383f0d9b81eca17c3fcb80508e01b11d9ca956d790c41baefb859d7d",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "bb5c5d1ea0f199fe2d3f0996fff4b48ca6ddc415a3dbd98f50bff7fce48aac80",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "936b624c2512a3a3370aae8adf603d6ae71ba8ebd39cc4714a13306891ea36f0",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "c9ffe9c2c88685ce3064f734cbdfede0a07de7d826fada58f8045f3bd8f81a9d",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "e156b972b72ae2703c13da3335b16ce5db9f1f33bac27cb0c444a59d04d918fc",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-i686-unknown-linux-gnu-install_only.tar.gz",
            "82de7e2551c015145c017742a5c0411d67a7544595df43c02b5efa4762d5123e",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "e43d70a49919641ca2939a5a9107b13d5fef8c13af0f511a33a94bb6af2044f0",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-x86_64-apple-darwin-install_only.tar.gz",
            "4a4efa7378c72f1dd8ebcce1afb99b24c01b07023aa6b8fea50eaedb50bf2bfc",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "00f002263efc8aea896bcfaaf906b1f4dab3e5cd3db53e2b69ab9a10ba220b97",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "6e4d20e6d498f9edeb3c28cb9541ad20f675f16da350b078e40a9dcfd93cdc3d",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "556d7d46c2af6f9744da03cac5304975f60de1cd5846a109814dd5c396fe9042",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.11.5%2B20230826-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "fbed6f7694b2faae5d7c401a856219c945397f772eea5ca50c6eb825cbc9d1e1",
        ),
    },
    PythonVersion("cpython", 3, 11, 4, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "988d476c806f71a3233ff4266eda166a5d28cf83ba306ac88b4220554fc83e8c",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-aarch64-apple-darwin-install_only.tar.gz",
            "cb6d2948384a857321f2aa40fa67744cd9676a330f08b6dad7070bda0b6120a4",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "46982228f02dc6d8a1227289de479f938567ec8acaa361909a998a0196823809",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "2e84fc53f4e90e11963281c5c871f593abcb24fc796a50337fa516be99af02fb",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "e2f4b41c3d89c5ec735e2563d752856cb3c19a0aa712ec7ef341712bafa7e905",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "0d22f43c5bb3f27ff2f9e8c60b0d7abd391bb2cac1790b0960970ff5580f6e9a",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "1bf5ba6806abbe70770e8e00b2902cbbb75dd4ff0c6e992de85e6752a9998e1a",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-i686-unknown-linux-gnu-install_only.tar.gz",
            "abdccc6ec7093f49da99680f5899a96bff0b96fde8f5d73f7aac121e0d05fdd8",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "6d9765785316c7f1c07def71b413c92c84302f798b30ee09e2e0b5da28353a51",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-x86_64-apple-darwin-install_only.tar.gz",
            "47e1557d93a42585972772e82661047ca5f608293158acb2778dccf120eabb00",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "878614c03ea38538ae2f758e36c85d2c0eb1eaaca86cd400ff8c76693ee0b3e1",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "1692d795d6199b2261161ae54250009ffad0317929302903f6f2c773befd4d76",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "b48061173c763971a28669585b47fa26cde98497eee6ebd8057849547b7282ee",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.11.4%2B20230726-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "e26247302bc8e9083a43ce9e8dd94905b40d464745b1603041f7bc9a93c65d05",
        ),
    },
    PythonVersion("cpython", 3, 11, 3, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "cd296d628ceebf55a78c7f6a7aed379eba9dbd72045d002e1c2c85af0d6f5049",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-aarch64-apple-darwin-install_only.tar.gz",
            "09e412506a8d63edbb6901742b54da9aa7faf120b8dbdce56c57b303fc892c86",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "8b8e4c58070f8ff372cf89080f24ecb9154ccfcc7674a8a46d67bdb766a1ee95",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "8190accbbbbcf7620f1ff6d668e4dd090c639665d11188ce864b62554d40e5ab",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "a6751e6fa5c7c4d4748ed534a7f00ad7f858f62ce73d63d44dd907036ba53985",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "877c90ef778a526aa25ab417034f5e70728ac14e5eb1fa5cfd741f531203a3fc",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "58734b66ee8d2762911f32c6bf59f36928990dc637e494f9ac8ebdd589d64547",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-i686-unknown-linux-gnu-install_only.tar.gz",
            "36ff6c5ebca8bf07181b774874233eb37835a62b39493f975869acc5010d839d",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "2fbb31a8bc6663e2d31d3054319b51a29b1915c03222a94b9d563233e11d1bef",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-x86_64-apple-darwin-install_only.tar.gz",
            "f710b8d60621308149c100d5175fec39274ed0b9c99645484fd93d1716ef4310",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "24741066da6f35a7ff67bee65ce82eae870d84e1181843e64a7076d1571e95af",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "9d27e607fb1cb2d766e17f27853013d8c0f0b09ac53127aaff03ec89ab13370d",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "b9e2e889a5797b181f086c175a03a0e011277a708199b2b20270bacfca72fb91",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.11.3%2B20230507-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "da50b87d1ec42b3cb577dfd22a3655e43a53150f4f98a4bfb40757c9d7839ab5",
        ),
    },
    PythonVersion("cpython", 3, 11, 1, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "da187194cc351d827232b1d2d85b2855d7e25a4ada3e47bc34b4f87b1d989be5",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-aarch64-apple-darwin-install_only.tar.gz",
            "4918cdf1cab742a90f85318f88b8122aeaa2d04705803c7b6e78e81a3dd40f80",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "cd3b910dce032f0ec9b414156b391878010940368b5ea27c33b998016e9c1cb8",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "debf15783bdcb5530504f533d33fda75a7b905cec5361ae8f33da5ba6599f8b4",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "50b250dd261c3cca9ae8d96cb921e4ffbc64f778a198b6f8b8b0a338f77ae486",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "b062ac2c72a85510fb9300675bd5c716baede21e9482ef6335247b4aa006584c",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "cce57c5fbd3ff10b91d86978b7ad15b9e02f57447d4f429c0bd4e00aa676d389",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-i686-unknown-linux-gnu-install_only.tar.gz",
            "8392230cf76c282cfeaf67dcbd2e0fac6da8cd3b3aead1250505c6ddd606caae",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "0eb61be53ee13cf75a30b8a164ef513a2c7995b25b118a3a503245d46231b13a",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-x86_64-apple-darwin-install_only.tar.gz",
            "20a4203d069dc9b710f70b09e7da2ce6f473d6b1110f9535fb6f4c469ed54733",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "edc08979cb0666a597466176511529c049a6f0bba8adf70df441708f766de5bf",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "f5c46fffda7d7894b975af728f739b02d1cec50fd4a3ea49f69de9ceaae74b17",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "02332441cb610b1e1aa2d2972e261e2910cc6a950b7973cac22c0759a93c5fcd",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.11.1%2B20230116-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "02a551fefab3750effd0e156c25446547c238688a32fabde2995c941c03a6423",
        ),
    },
    PythonVersion("cpython", 3, 10, 18, False): {
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-aarch64-unknown-linux-gnu-install_only_stripped.tar.gz",
            "f2df9f0cfdaa36197d5ec73aa36c3b74fab9d6aee8c12e374e02b04789109fbd",
        ),
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "9c5a6b35d1b3c59f004d01df89091b6398869071a76dce526a3fe92714150ae1",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-aarch64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "d2c5dd469df686ed66839bb4780dfb12dd4c33d09f22ef0ee7c951dd1fd5d3ba",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-aarch64-apple-darwin-install_only.tar.gz",
            "11ff3525bf15fd5ad8aec7a00fd3c6351e44d7ff0d35d43ac9cbdd57c865b19c",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-i686-pc-windows-msvc-install_only.tar.gz",
            "0b2674627c939ac9d333eccb15f08cae5ff25fca1b37c6a7d38ff034f40d4155",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-i686-pc-windows-msvc-pgo-full.tar.zst",
            "b87fa95d9b5f23c694e04f8496feab83fb725e9af7cea097d850cf433d9600f0",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "71b48e895f145bad3fffac46d3ddd914f4b498b797c398c798c4e5a2c4cb0741",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-x86_64-apple-darwin-install_only.tar.gz",
            "22f56e0135be75e14e1508b8c9d144cce6766e46344a0d50a198757fca176f7d",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-x86_64-pc-windows-msvc-install_only.tar.gz",
            "5be6d80ea9d6aed69dfeb5ca3ff7e0ecb7976c5fc8525b183b0b1ef7d74fad1a",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-x86_64-pc-windows-msvc-pgo-full.tar.zst",
            "96e0fd7bb8360115eff03f8ca5ffe54b8667272a494d187702bab9e1b55d0db0",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "2252b2a4953a67a7fa20c070ae8e015c89a5951021444eda034bad1670e3abd8",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.10.18%2B20250723-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "3d725d40dbd4cabfc84fadeade3acc2fcacd9cd7caad7aeba5c3a195b45d6cc2",
        ),
    },
    PythonVersion("cpython", 3, 10, 17, False): {
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-aarch64-unknown-linux-gnu-install_only_stripped.tar.gz",
            "aaf1fd370ab3ae31807c8d7d5aff0b8d2abb370c12d17a561178ceb842314f2a",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-aarch64-apple-darwin-install_only_stripped.tar.gz",
            "366d037181b1cea0a7a8b1457874a0cdfbb795815d07ae25c55ef1461aa487ef",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "1fb40c398fd1237c762dd0365b7c7e2cf4c4870d53200cdb78d2e98f5340308f",
        ),
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "de00878c13f790b6ef5054a0a19807146c71d81af57d790646d722145450336d",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-i686-pc-windows-msvc-install_only.tar.gz",
            "6db11c7153d6b74accbf218ae5aedf1623723bab8030b6474bd46cefb0572f68",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-i686-pc-windows-msvc-pgo-full.tar.zst",
            "3fb73c6c928fa1d8df1a503a8d2a52c61b22a14168a0d772333d1d37499a153e",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "ce5ea4c2da1f39686fbb41751b00714d30b092733143c79df15fdbcb31fc2500",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-x86_64-apple-darwin-install_only.tar.gz",
            "22f43d26ec2bbf1a2bc21d80710031aa30fbadef6ec5b2b6fbde3375bde1fea8",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-x86_64-pc-windows-msvc-install_only.tar.gz",
            "4d2eb31500ec365520a899a040172c678b4ae9cfbbcb71075bc095a151809d10",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-x86_64-pc-windows-msvc-pgo-full.tar.zst",
            "acd379ae2cdf8c131ef678401faf49292ce610de6a27aac04f45dd96adfd07ae",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "9cb2bb22ae1e6e49a1789cad8c0343f72a38a77cdc7c3da34a542737e6984ba8",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.10.17%2B20250529-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "6634b2d2504fa07ec62655dc8a0266c2705a3920a1e08519e5c6730a79f8a2c1",
        ),
    },
    PythonVersion("cpython", 3, 10, 16, False): {
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.10.16%2B20250317-aarch64-unknown-linux-gnu-install_only_stripped.tar.gz",
            "5dcd0486c403d07e27a1f2c256810122ac5d153f921433d77116df5c7d09b4c9",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.10.16%2B20250317-aarch64-apple-darwin-install_only.tar.gz",
            "e99f8457d9c79592c036489c5cfa78df76e4762d170665e499833e045d82608f",
        ),
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.10.16%2B20250317-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "d370266f6fccd315cfd4fb44198c81485e1fa1e0ae2827b2562a1b6bc0447706",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.10.16%2B20250317-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "3aa8f90fdc2eb6254319dfde900e4568717970d8b4019752e86d4acce5c086e6",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.10.16%2B20250317-i686-pc-windows-msvc-install_only.tar.gz",
            "70914bddcdd4f49b24e87728308995925931e4768eb59343bda7a605b74bf03b",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250212/cpython-3.10.16%2B20250212-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "bd4f387f3934ffe64d16d97e3bb861c7773ac6efeec3511dce9c7f79beb88380",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.10.16%2B20250317-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "56889b1ba79ebb31aac39574904b4797589783745ce797b3276e8be6db2b66b3",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.10.16%2B20250317-x86_64-apple-darwin-install_only.tar.gz",
            "e03e62dbe95afa2f56b7344ff3bd061b180a0b690ff77f9a1d7e6601935e05ca",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.10.16%2B20250317-x86_64-pc-windows-msvc-install_only.tar.gz",
            "c7e0eb0ff5b36758b7a8cacd42eb223c056b9c4d36eded9bf5b9fe0c0b9aeb08",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250212/cpython-3.10.16%2B20250212-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "2d2a9acb0a1c5ccb55e0422b6dac3bfd4e2b52b5b879095f237da50e7615857a",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.10.16%2B20250317-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "081b27bc332bbd27e5867f2e1731b44e020a61e8f203e6937f14d240db14255b",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.10.16%2B20250317-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "b350c7e63956ca8edb856b91316328e0fd003a840cbd63d08253af43b2c63643",
        ),
    },
    PythonVersion("cpython", 3, 10, 15, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "41c84c6a96f45e6eaa5ff012806ae180a5a758e0c8027b88295b7c2a28a1c82a",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-aarch64-apple-darwin-install_only.tar.gz",
            "f64776f455a44c24d50f947c813738cfb7b9ac43732c44891bc831fa7940a33c",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "1ee866fe8c3a2fdf310defd9492aaa9d4d26b93b8ee9573525a860cdeacfc8fe",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "eb58581f85fde83d1f3e8e1f8c6f5a15c7ae4fdbe3b1d1083931f9167fdd8dbc",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-i686-pc-windows-msvc-install_only.tar.gz",
            "c75ae93c20e03ea6b7521d7ebf07bdf30ff915d19cb166afa483576150e7aef8",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "50301fa93816feeadddcbf0bc035870da1ef3006814c59b874ddf11252f19be3",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "adaa44bb698b4b0a38335114bc9c2e53505f3562cb39aac35c87b7978175a9d1",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-x86_64-apple-darwin-install_only.tar.gz",
            "90b46dfb1abd98d45663c7a2a8c45d3047a59391d8586d71b459cec7b75f662b",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-x86_64-pc-windows-msvc-install_only.tar.gz",
            "e48952619796c66ec9719867b87be97edca791c2ef7fbf87d42c417c3331609e",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "ade7ce8507635e64b6d167da0dced735816a4e1c121433277a0ed86e42887d46",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "8438e1c8df6180dbd4daa8363ac4b156de5cd9cbcdfd37e4a7052903457ae6b4",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.10.15%2B20241016-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "3db2171e03c1a7acdc599fba583c1b92306d3788b375c9323077367af1e9d9de",
        ),
    },
    PythonVersion("cpython", 3, 10, 14, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "ca1bf0cf71ef42a302bc9df6096ac6ae399b971b426e7b620481d86b00fffa8c",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-aarch64-apple-darwin-install_only.tar.gz",
            "c9fbd43c47ebc15f3b326d518e3f4df2fed98db226cd16f97ecbcc496c099d5d",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "56183ed135dbdaaa6b189a6b0e6b6bcf3f26074297721bdbe9648cdc5247937c",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "232921e70965f78dc32e2ed4d15f97b45d24ac68dbc5d6f4ff402f81bad11eda",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-i686-pc-windows-msvc-install_only.tar.gz",
            "682d90512c7fec569a0380c3ca5707063b23659ff41fdbcf25c3a18777962e72",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "49ba7ea8de4ca7369be9c3415712d789db3caaa4c6c0530ce94d2db5b4e145cd",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "75b7453594f41a4d6e50efb643b1717067642ad3cd85fc6151dec1d4fcb15810",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-x86_64-apple-darwin-install_only.tar.gz",
            "9e3df0cd6624f7943e1c4b72527356e1ad6edb59b3cf36562e0d380eb3e2b424",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-x86_64-pc-windows-msvc-install_only.tar.gz",
            "e5982ec53bed31a3f9c1f063cb5fb031089ae436a8541cd471fe660836dddbbb",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "ffd0f253678f20580acb90680138d990f37518cbb622e5a4032759d5b06ff9fe",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "e80e39be6f3fe2620c210889d13041aac16573ebac103f7bbaafeedc0d8fc253",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.10.14%2B20240814-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "88dee5e48def031cdaebdedeae13865f086b532161315dda7f39c1428dfef64f",
        ),
    },
    PythonVersion("cpython", 3, 10, 13, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "57b83a4aa32bdbe7611f1290313ef24f2574dff5fa59181c0ccb26c14c688b73",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-aarch64-apple-darwin-install_only.tar.gz",
            "5fdc0f6a5b5a90fd3c528e8b1da8e3aac931ea8690126c2fdb4254c84a3ff04a",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "7f23a4afa4032a7c5a4e0ec926da37eea242472142613c2baa029ef61c3c493c",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "a898a88705611b372297bb8fe4d23cc16b8603ce5f24494c3a8cfa65d83787f9",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "5365b90f9cba7186d12dd86516ece8b696db7311128e0b49c92234e01a74599f",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "c8b99dcf267c574fdfbdf4e9d63ec7a4aa4608565fee3fba0b2f73843b9713b2",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "a41c1e28e2a646bac69e023873d40a43c5958d251c6adfa83d5811a7cb034c7a",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-x86_64-apple-darwin-install_only.tar.gz",
            "6378dfd22f58bb553ddb02be28304d739cd730c1f95c15c74955c923a1bc3d6a",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "086f7fe9156b897bb401273db8359017104168ac36f60f3af4e31ac7acd6634e",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "6a2c8f37509556e5d463b1f437cdf7772ebd84cdf183c258d783e64bb3109505",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "d42f0dfa0245eb5d7cf26e86ce21ce6a92efb85bb2fb26c79a4657f18bae5fa1",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.10.13%2B20240224-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "d995d032ca702afd2fc3a689c1f84a6c64972ecd82bba76a61d525f08eb0e195",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.10.13%2B20230826-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "cc5625a16fbec682d4ce40c0d185318164bd181efaa7eaf945ca63015db9fea3",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.10.13%2B20230826-i686-unknown-linux-gnu-install_only.tar.gz",
            "424d239b6df60e40849ad18505de394001233ab3d7470b5280fec6e643208bb9",
        ),
    },
    PythonVersion("cpython", 3, 10, 12, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "a7d0cadbe867cc53dd47d7327244154157a7cca02edb88cf3bb760a4f91d4e44",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-aarch64-apple-darwin-install_only.tar.gz",
            "bc66c706ea8c5fc891635fda8f9da971a1a901d41342f6798c20ad0b2a25d1d6",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "bb5fa1d4ad202afc8ee4330f313c093760c9fb1af5be204dc0c6ba50c7610fea",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "fee80e221663eca5174bd794cb5047e40d3910dbeadcdf1f09d405a4c1c15fe4",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "a5a5f9c9082b6503462a6b134111d3c303052cbc49ff31fff2ade38b39978e5d",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "0743b9976f20b06d9cf12de9d1b2dfe06b13f76978275e9dac73a275624bde2c",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "159124ac71c86d8617eae17db6ed9b98f01078cc9bd76073261901826f2d940d",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-i686-unknown-linux-gnu-install_only.tar.gz",
            "c7a5321a696ef6467791312368a04d36828907a8f5c557b96067fa534c716c18",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "f1fa448384dd48033825e56ee6b5afc76c5dd67dcf2b73b61d2b252ae2e87bca",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-x86_64-apple-darwin-install_only.tar.gz",
            "8a6e3ed973a671de468d9c691ed9cb2c3a4858c5defffcf0b08969fba9c1dd04",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "c1a31c353ca44de7d1b1a3b6c55a823e9c1eed0423d4f9f66e617bdb1b608685",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "cb6e7c84d9e369a0ee76c9ea73d415a113ba9982db58f44e6bab5414838d35f3",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "79fe684338fa26e1af64de583cca77a3fd501d899420de398177952d5182d202",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.10.12%2B20230726-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "a476dbca9184df9fc69fe6309cda5ebaf031d27ca9e529852437c94ec1bc43d3",
        ),
    },
    PythonVersion("cpython", 3, 10, 11, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "da9c8a3cd04485fd397387ea2fa56f3cac71827aafb51d8438b2868f86eb345b",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-aarch64-apple-darwin-install_only.tar.gz",
            "8348bc3c2311f94ec63751fb71bd0108174be1c4def002773cf519ee1506f96f",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "2e304c39d8af27f9abf1cf44653f5e34e7d05b665cb68e5a5474559c145e7b33",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "c7573fdb00239f86b22ea0e8e926ca881d24fde5e5890851339911d76110bc35",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "e4ed3414cd0e687017f0a56fed88ff39b3f5dfb24a0d62e9c7ca55854178bcde",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "60e76e136ab23b891ed1212e58bd11a73a19cd9fd884ec1c5653ca1c159d674e",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "f55942f89c54c90af53dba603a86f90956eec87c7fb91f5dc2ae543373224ccd",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-i686-unknown-linux-gnu-install_only.tar.gz",
            "c70518620e32b074b1b40579012f0c67191a967e43e84b8f46052b6b893f7eeb",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "e84c12aa0285235eed365971ceedf040f4d8014f5342d371e138a4da9e4e9b7c",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-x86_64-apple-darwin-install_only.tar.gz",
            "bd3fc6e4da6f4033ebf19d66704e73b0804c22641ddae10bbe347c48f82374ad",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "9c2d3604a06fcd422289df73015cd00e7271d90de28d2c910f0e2309a7f73a68",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "9b4dc4a335b6122ce783bc80f5015b683e3ab1a56054751c5df494db0521da67",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "38931a156ed020f5c579af37b771871b99f31e74c34fa7e093e97eb1b2d4f978",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.10.11%2B20230507-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "c5bcaac91bc80bfc29cf510669ecad12d506035ecb3ad85ef213416d54aecd79",
        ),
    },
    PythonVersion("cpython", 3, 10, 9, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "2508b8d4b725bb45c3e03d2ddd2b8441f1a74677cb6bd6076e692c0923135ded",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-aarch64-apple-darwin-install_only.tar.gz",
            "018d05a779b2de7a476f3b3ff2d10f503d69d14efcedd0774e6dab8c22ef84ff",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "3d20f40654e4356bd42c4e70ec28f4b8d8dd559884467a4e1745c08729fb740a",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "2003750f40cd09d4bf7a850342613992f8d9454f03b3c067989911fb37e7a4d1",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "c5c51d9a3e8d8cdac67d8f3ad7c4008de169ff1480e17021f154d5c99fcee9e3",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "3d79cfd229ec12b678bbfd79c30fb4cbad9950d6bfb29741d2315b11839998b4",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "ae0745620168e65df44ae60b21622d488c9dd6ca83566083c565765256315283",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-i686-unknown-linux-gnu-install_only.tar.gz",
            "44566c08eb8054aa0784f76b85d2c6c70a62f4988d5e9abcce819b517b329fdd",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "1153b4d3b03cf1e1d8ec93c098160586f665fcc2d162c0812140a716a688df58",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-x86_64-apple-darwin-install_only.tar.gz",
            "0e685f98dce0e5bc8da93c7081f4e6c10219792e223e4b5886730fd73a7ba4c6",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "59c6970cecb357dc1d8554bd0540eb81ee7f6d16a07acf3d14ed294ece02c035",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "4cfa6299a78a3959102c461d126e4869616f0a49c60b44220c000fc9aecddd78",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "c5f7ad956c8870573763ed58b59d7f145830a93378234b815c068c893c0d5c1e",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230116/cpython-3.10.9%2B20230116-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "d196347aeb701a53fe2bb2b095abec38d27d0fa0443f8a1c2023a1bed6e18cdf",
        ),
    },
    PythonVersion("cpython", 3, 10, 8, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "f8ba5f87153a17717e900ff7bba20e2eefe8a53a5bd3c78f9f6922d6d910912d",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-aarch64-apple-darwin-install_only.tar.gz",
            "d52b03817bd245d28e0a8b2f715716cd0fcd112820ccff745636932c76afa20a",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "5710521ca6958dd2e50f30f2b1591eb7f6a4c55a64c9b66d3196f8257f40bc96",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "33170bef18c811906b738be530f934640491b065bf16c4d276c6515321918132",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "94e76273166f72624128e52b5402db244cea041dab4a6bcdc70b304b66e27e95",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "7547ea172f7fa3d7619855f28780da9feb615b6cb52c5c64d34f65b542799fee",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "0ab3156bbdc87db8a9b938662a76bb405522b408b1f94d8eb20759f277f96cd8",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-i686-unknown-linux-gnu-install_only.tar.gz",
            "2deee7cbbd5dad339d713a75ec92239725d2035e833af5b9981b026dee0b9213",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "a18f81ecc7da0779be960ad35c561a834866c0e6d1310a4f742fddfd6163753f",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-x86_64-apple-darwin-install_only.tar.gz",
            "525b79c7ce5de90ab66bd07b0ac1008bafa147ddc8a41bef15ffb7c9c1e9e7c5",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "f2b6d2f77118f06dd2ca04dae1175e44aaa5077a5ed8ddc63333c15347182bfe",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "ab40f9584be896c697c5fca351ab82d7b55f01b8eb0494f0a15a67562e49161a",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "59630be21c77f87b4378f0cf887cbeb6bec64c988c93f3dc795afee782a3322e",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.10.8%2B20221106-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "6c8db44ae0e18e320320bbaaafd2d69cde8bfea171ae2d651b7993d1396260b7",
        ),
    },
    PythonVersion("cpython", 3, 10, 7, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "9f44cf63441a90f4ec99a032a2bda43971ae7964822daa0ee730a9cba15d50da",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-aarch64-apple-darwin-install_only.tar.gz",
            "70f6ca1da8e6fce832ad0b7f9fdaba0b84ba0ac0a4c626127acb6d49df4b8f91",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "f92fb53661f2ceddeb7b15ae1f165671acf4e4d4f9519a87e033981b93ee33b8",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "dfeec186a62a6068259d90e8d77e7d30eaf9c2b4ae7b205ff8caab7cb21f277c",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "384e711dd657c3439be4e50b2485478a7ed7a259a741d4480fc96d82cc09d318",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "323532701cb468199d6f14031b991f945d4bbf986ca818185e17e132d3763bdf",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "c379f2ef58c8d83f1607357ad75e860770d748232a4eec4263564cbfa6a3efbb",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-i686-unknown-linux-gnu-install_only.tar.gz",
            "4a611ce990dc1f32bc4b35d276f04521464127f77e1133ac5bb9c6ba23e94a82",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "e03e28dc9fe55ea5ca06fece8f2f2a16646b217d28c0cd09ebcd512f444fdc90",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-x86_64-apple-darwin-install_only.tar.gz",
            "6101f580434544d28d5590543029a7c6bdf07efa4bcdb5e4cbedb3cd83241922",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "b464352f8cbf06ab4c041b7559c9bda7e9f6001a94f67ab0a342cba078f3805f",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "5363974e6ee6c91dbd6bc3533e38b02a26abc2ff1c9a095912f237b916be22d3",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "22e59fa43657dc3487392a44a33a815d507cdd244b6609b6ad08f2661c34169c",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.10.7%2B20221002-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "c12c9ad2b2c75464541d897c0528013adecd8be5b30acf4411f7759729841711",
        ),
    },
    PythonVersion("cpython", 3, 10, 6, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "159230851a69cf5cab80318bce48674244d7c6304de81f44c22ff0abdf895cfa",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-aarch64-apple-darwin-install_only.tar.gz",
            "efaf66acdb9a4eb33d57702607d2e667b1a319d58c167a43c96896b97419b8b7",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "6606be4283ebcfe2d83b49b05f6d06b958fe120a4d96c1eeeb072369db06b827",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "81625f5c97f61e2e3d7e9f62c484b1aa5311f21bd6545451714b949a29da5435",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "27f22babf29ceebae18b2c2e38e2c48d22de686688c8a31c5f8d7d51541583c1",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "8d9a259e15d5a1be48ef13cd5627d7f6c15eadf41a3539e99ed1deee668c075e",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "213374fd9845df5c1d3f1d2f5ac2610fe70ddba094aee0cbc2e91fd2dc808de2",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-i686-unknown-linux-gnu-install_only.tar.gz",
            "b152801a2609e6a38f3cc9e7e21d8b6cf5b6f31dacfcaca01e162c514e851ed6",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "9405499573a7aa8b67d070d096ded4f3e571f18c2b34762606ecc8025290b122",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-x86_64-apple-darwin-install_only.tar.gz",
            "7718411adf3ea1480f3f018a643eb0550282aefe39e5ecb3f363a4a566a9398c",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "91889a7dbdceea585ff4d3b7856a6bb8f8a4eca83a0ff52a73542c2e67220eaa",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "01dc349721594b1bb5b582651f81479a24352f718fdf6279101caa0f377b160a",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "8072f01279e05bad7c8d1076715db243489d1c2598f7b7d0457d0cac44fcb8b2",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.10.6%2B20220802-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "55aa2190d28dcfdf414d96dc5dcea9fe048fadcd583dc3981fec020869826111",
        ),
    },
    PythonVersion("cpython", 3, 10, 5, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "f68d25dbe9daa96187fa9e05dd8969f46685547fecf1861a99af898f96a5379e",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-aarch64-apple-darwin-install_only.tar.gz",
            "19d1aa4a6d9ddb0094fc36961b129de9abe1673bce66c86cd97b582795c496a8",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "6e5e1050549c1aa629924b1b6a3080655d9e110f88dfa734d9b1c98af924cc7d",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "012fa37c12d2647d76d004dc003302563864d2f1cd0731b71eeafad63d28b3f0",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "2846e9c7e8484034989ab218022009fdd9dcb12a7bfb4b0329a404552d37e9aa",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "e201192f0aa73904bc5a5f43d1ce4c9fb243dfe02138e690676713fe02c7d662",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "dea116554852261e4a9e79c8926a0e4ac483f9e624084ded73b30705e221b62d",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-i686-unknown-linux-gnu-install_only.tar.gz",
            "5abf5baf40f8573ce7d7e4ad323457f511833e1663e61ac5a11d5563a735159f",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "5e372e6738a733532aa985730d9a47ee4c77b7c706e91ef61d37aacbb2e54845",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-x86_64-apple-darwin-install_only.tar.gz",
            "eca0584397d9a3ef6f7bb32b0476318b01c89b7b0a031ef97a0dcaa5ba5127a8",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "c830ab2a3a488f9cf95e4e81c581d9ef73e483c2e6546136379443e9bb725119",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "cff35feefe423d4282e9a3e1bb756d0acbb2f776b1ada82c44c71ac3e1491448",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "2a71e32ef8e1bbffbbfcd1825620d6a8944f97e76851bf1a14dc4fa48b626db8",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220630/cpython-3.10.5%2B20220630-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "460f87a389be28c953c24c6f942f172f9ce7f331367b4daf89cb450baedd51d7",
        ),
    },
    PythonVersion("cpython", 3, 10, 4, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "c404f226195d79933b1e0a3ec88f0b79d35c873de592e223e11008f3a37f83d6",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-aarch64-apple-darwin-install_only.tar.gz",
            "6d2e4e6b1c403bce84cfb846400754017f525fe8017f186e8e7072fcaaf3aa71",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "5d2ccef5a45d2287d73a6ff63a466b21a197beb373792e644b8881bce3b6aa55",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "7a8989392dc9b41d85959a752448c60852cf0061de565e98445c27f6bbdf63be",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "e1dfa5dde910f908cad8bd688b29d28df832f7b150555679c204580d1af0c4a6",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "c37a47e46de93473916f700a790cb43515f00745fba6790004e2731ec934f4d3",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "b28224a798dea965cb090f831d31aa531c6b9a14028344be6df53ab426497bb4",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-i686-unknown-linux-gnu-install_only.tar.gz",
            "f3bc0828a0e0a8974e3fe90b4e99549296a7578de2321d791be1bad28191921d",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "e447f00fe53168d18cbfe110645dbf33982a17580b9e4424a411f9245d99cd21",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-x86_64-apple-darwin-install_only.tar.gz",
            "c4a57a13b084d49ce8c2eb5b2662ee45b0c55b08ddd696f473233b0787f03988",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "7231ba2af9525cae620a5f4ae3bf89a939fdc053ba0cc64ee3dead8f13188005",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "d636dc1bcca74dd9c6e3b26f7c081b3e229336e8378fe554bf8ba65fe780a2ac",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "15f961b087c6145f326fee30041db4af3ce0a8d24bbdefbd8d24973825728a0e",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220528/cpython-3.10.4%2B20220528-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "1f8423808ad84c0e56c8e14c32685cbfbc1159e0d9f943ac946f29e84cf1b5ee",
        ),
    },
    PythonVersion("cpython", 3, 10, 3, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "b1abefd0fc66922cf9749e4d5ceb97df4d3cfad0cd9cdc4bd04262a68d565698",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-aarch64-apple-darwin-install_only.tar.gz",
            "db46dadfccc407aa1f66ed607eefbf12f781e343adcb1edee0a3883d081292ce",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "88d2bfc8b714b9e36e95e68129799527077827dd752357934f9d3d0ce756871e",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "f52ee68c13c4f9356eb78a5305d3178af2cb90c38a8ce8ce9990a7cf6ff06144",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "bb7f2a5143010fa482c5b442cced85516696cfc416ca92c903ef374532401a33",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "fbc0924a138937fe435fcdb20b0c6241290558e07f158e5578bd91cc8acef469",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "ea82b0b12e03fdc461c2337e59cb901ecc763194588db5a97372d26f242f4951",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-i686-unknown-linux-gnu-install_only.tar.gz",
            "2f125a927c3af52ef89af11857df988a042e26ce095129701b915e75b2ec6bff",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "bc5d6f284b506104ff6b4e36cec84cbdb4602dfed4c6fe19971a808eb8c439ec",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-x86_64-apple-darwin-install_only.tar.gz",
            "ec2e90b6a589db7ef9f74358b1436558167629f9e4d725c8150496f9cb08a9d4",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "ba593370742ed8a7bc70ce563dd6a53e30ece1f6881e3888d334c1b485b0d9d0",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "72b91d26f54321ba90a86a3bbc711fa1ac31e0704fec352b36e70b0251ffb13c",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "ee2251d5e59045c6fa1d4431c8a5cd0ed18923a785e7e0f47aa9d32ae0ca344e",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.10.3%2B20220318-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "b9989411bed71ba4867538c991f20b55f549dd9131905733f0df9f3fde81ad1d",
        ),
    },
    PythonVersion("cpython", 3, 10, 2, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "1ef939fd471a9d346a7bc43d2c16fb483ddc4f98af6dad7f08a009e299977a1a",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-aarch64-apple-darwin-install_only.tar.gz",
            "1409acd9a506e2d1d3b65c1488db4e40d8f19d09a7df099667c87a506f71c0ef",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "fb714771145a49482a113f532e4cbc21d601cf0dee4186a57fbc66ddd8d85aef",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "8f351a8cc348bb45c0f95b8634c8345ec6e749e483384188ad865b7428342703",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "5321f8c2c71239b1e2002d284be8ec825d4a6f95cd921e58db71f259834b7aa1",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "698b09b1b8321a4dc43d62f6230b62adcd0df018b2bcf5f1b4a7ce53dcf23bcc",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "817cc2720c9c67cf87e5c0e41e44111098ceb6372d8140c8adbdd2f0397f1e02",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-i686-unknown-linux-gnu-install_only.tar.gz",
            "4fa49dab83bf82409816db431806525ce894280a509ca96c91e3efc9beed1fea",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "bacf720c13ab67685a384f1417e9c2420972d88f29c8b7c26e72874177f2d120",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-x86_64-apple-darwin-install_only.tar.gz",
            "8146ad4390710ec69b316a5649912df0247d35f4a42e2aa9615bffd87b3e235a",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "a1d9a594cd3103baa24937ad9150c1a389544b4350e859200b3e5c036ac352bd",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "7397e78a4fbe429144adc1f33af942bdd5175184e082ac88f3023b3a740dd1a0",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "65d2a31c3181ab15342e60a2ef92d6a0df6945200191115d0303d6e77428521c",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.10.2%2B20220227-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "9b64eca2a94f7aff9409ad70bdaa7fbbf8148692662e764401883957943620dd",
        ),
    },
    PythonVersion("cpython", 3, 10, 0, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.10.0-aarch64-apple-darwin-pgo%2Blto-20211017T1616.tar.zst",
            None,
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.10.0-aarch64-apple-darwin-install_only-20211017T1616.tar.gz",
            None,
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.10.0-aarch64-unknown-linux-gnu-lto-20211017T1616.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.10.0-i686-pc-windows-msvc-shared-pgo-20211017T1616.tar.zst",
            None,
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.10.0-i686-unknown-linux-gnu-pgo%2Blto-20211017T1616.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.10.0-x86_64-apple-darwin-pgo%2Blto-20211017T1616.tar.zst",
            None,
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.10.0-x86_64-apple-darwin-install_only-20211017T1616.tar.gz",
            None,
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.10.0-x86_64-pc-windows-msvc-shared-install_only-20211017T1616.tar.gz",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.10.0-x86_64-pc-windows-msvc-shared-pgo-20211017T1616.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.10.0-x86_64-unknown-linux-gnu-pgo%2Blto-20211017T1616.tar.zst",
            None,
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.10.0-x86_64-unknown-linux-gnu-install_only-20211017T1616.tar.gz",
            None,
        ),
    },
    PythonVersion("cpython", 3, 9, 23, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "344790862ba71267f386c9275b4f4eccfcf10bbc8978567554806abd7fc76a4b",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-aarch64-apple-darwin-install_only.tar.gz",
            "98c368cf68a05070b72021dabe990a74f8532ae8f84a505c89d07a76c577422a",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-aarch64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "3a5811dba042fd923da2f7c1861315ed8e7275abf5e50e3864a6e43fb5eeedee",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "c2e564c2c3043e08a3441cc0ac7a318c8e5283e5f3c261d1e97e5504563b2ba6",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-i686-pc-windows-msvc-install_only.tar.gz",
            "a21f11cb08ea0cb01deb05ee84af9ba4f4687663985fe5eecd3304fa77758b23",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-i686-pc-windows-msvc-pgo-full.tar.zst",
            "40a7a68c9a3f83fe76376565064303eb4740a6fd170227d9ec0725c41eb1babc",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "f0097ca8f2cfab47624019ba14a3e9430d5991e70e25d9ec40fe28ee8b833979",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-x86_64-apple-darwin-install_only.tar.gz",
            "a6e0b1440b3fd370f16855df1f2dc0bb5d9e534e1179ecd97dada2c0ff3cd4fd",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-x86_64-pc-windows-msvc-install_only.tar.gz",
            "093ee21cdb8f9eaf4b6b80eeb7ea1487176336ca6b32c632d8dec3a4dca5c038",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-x86_64-pc-windows-msvc-pgo-full.tar.zst",
            "dfa594a3eca87610640f84bdb2e1d7fb361f47e0e143ef2b4448bcea9dae7d9a",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "190ce50585a8375ae51f42d0077a89aeabe4266f5efe4b74ef399c9e310a0ef0",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250723/cpython-3.9.23%2B20250723-x86_64-unknown-linux-gnu-install_only_stripped.tar.gz",
            "d7b9149e48776712fdcb314e0509d81277ba3d068fcf247d0ee8ad255896be42",
        ),
    },
    PythonVersion("cpython", 3, 9, 22, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "dd7f4fa4d0317109b5d82f3e03d91a81ba8ee8faefc3a2558fe65e6780fe8ebf",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-aarch64-apple-darwin-install_only.tar.gz",
            "29a81900ec2f964538ddd404536da57c2aa816901713e4c03bca3678cae25cb2",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "5b5ba6b397f3b83f9d6ba5581744c93c0fc983de319aadd6756ec64daf58a1a7",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "4020998db84d5400c1ee68fba1601d37678bf8bdbdf637b4eff5729e7cedda82",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-i686-pc-windows-msvc-install_only.tar.gz",
            "8d6950daf8e75cf1784b23c86cae095749654b128060fb4c9726d0ac74cfdc8a",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-i686-pc-windows-msvc-pgo-full.tar.zst",
            "d0c19875e07b15bb192b0cb1ed13f4989b24de67dea735f847e89c336cf52174",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "ef3b4fe84fe284de99d8513860c8a22ce8a05155662fa1141db62d824079482f",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-x86_64-apple-darwin-install_only.tar.gz",
            "8a8276c460f98987d10595d6f3d1bbe6438a28ec4e989d8b02af1f8a7741497b",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-x86_64-pc-windows-msvc-install_only.tar.gz",
            "e54811a7377c54f12c9e86b9e1efad412d2bc5b99c2f080ee2dda287d9c2b730",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-x86_64-pc-windows-msvc-pgo-full.tar.zst",
            "ada41fe6cdee31aec3d8719190624ec6b3be61681c9cd906e6b51da03581bc55",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "b92e31b14691b0db0ad9a27951cf49da070e1cc25f11ce2c2c13d19f6862445a",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250529/cpython-3.9.22%2B20250529-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "aad1b7a4c416de187922128720d8396be5d02a6942a7b00b65fddf2805c0acf1",
        ),
    },
    PythonVersion("cpython", 3, 9, 21, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.9.21%2B20250317-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "bb30608cf0de9eb8efdfa41bcc1edf4cafd6d345667f68e8f927235033042e31",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.9.21%2B20250317-aarch64-apple-darwin-install_only.tar.gz",
            "2a7d83db10c082ce59e9c4b8bd6c5790310198fb759a7c94aceebac1d93676d3",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.9.21%2B20250317-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "3bd723cca8260428c01175c9723ee0c86cfce73d15be1a8ad490ce47d7d556f6",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.9.21%2B20250317-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "758ebbc4d60b3ca26cf21720232043ad626373fbeb6632122e5db622a1f55465",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.9.21%2B20250317-i686-pc-windows-msvc-install_only.tar.gz",
            "10801426bcaa3703cd153b78b0ff35e5b48c39559c8b534fca8373edd95e03db",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250212/cpython-3.9.21%2B20250212-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "4686987f21343d331627a2a5a9ad87f0ee049bf3ecd9473ea963edeea04888e6",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.9.21%2B20250317-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "992ae88b00798c4a18e7f231f7e2dc170a15f3c5b51b871a8af2c87e97749fbc",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.9.21%2B20250317-x86_64-pc-windows-msvc-install_only_stripped.tar.gz",
            "749f706985170981ae95f9227b1b412ae8acb33d8455cf34df427b50b0edd882",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.9.21%2B20250317-x86_64-unknown-linux-gnu-install_only_stripped.tar.gz",
            "837e50734904eba4f5a385c66c31b21d880582eba7912b3ea3d71e60e56f5a60",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.9.21%2B20250317-x86_64-apple-darwin-install_only.tar.gz",
            "786ebd91e4dd0920acf60aa3428a627a937342d2455f7eb5e9a491517c32db3d",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.9.21%2B20250317-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "0c97be33aac1eac7d195b9ad5ace367cb3be79b1f8329dcb22a4c482cbc02bab",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20250212/cpython-3.9.21%2B20250212-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "36b68a77fa1e835c3dd8b6155811b27ee7a9898b2c17dfca88c894f062267e08",
        ),
    },
    PythonVersion("cpython", 3, 9, 20, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "61ad2810005b6b66465da0f3055163812b0c089979a96b67ef54c34906c6f560",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-aarch64-apple-darwin-install_only.tar.gz",
            "34ab2bc4c51502145e1a624b4e4ea06877e3d1934a88cc73ac2e0fd5fd439b75",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "7f832256d0f2f1d0dc89f71c3baf2f84bbd32538891d5017163bebae60837d89",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "1e486c054a4e86666cf24e04f5e29456324ba9c2b95bf1cae1805be90d3da154",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-i686-pc-windows-msvc-install_only.tar.gz",
            "7c4bd39859b450e687a36a2fdee77fd22039b08b9769be4e90aab3eeee86117c",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "9c56fa3257cfe2147a5c17f8bd55870a305ffa1ef405e3f189e22989df15c573",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "a8bb2129da3bf93ae074775efc720d8857ac76475344ce0c73f0076884847ea1",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-x86_64-apple-darwin-install_only.tar.gz",
            "193dc7f0284e4917d52b17a077924474882ee172872f2257cfe3375d6d468ed9",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-x86_64-pc-windows-msvc-install_only.tar.gz",
            "5069008a237b90f6f7a86956903f2a0221b90d471daa6e4a94831eaa399e3993",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "7b7ba312e704bd60202af8a45d1019a4402569e6bbc9fa4feee24c86919c33bf",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "9873d6d3aa04918add2a7b428ab3d8b3baded2b0754c8fb97d6ed6b4201b0343",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241016/cpython-3.9.20%2B20241016-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "c20ee831f7f46c58fa57919b75a40eb2b6a31e03fd29aaa4e8dab4b9c4b60d5d",
        ),
    },
    PythonVersion("cpython", 3, 9, 19, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "f3a918ec61e0c1676c56cb2e3a29fce733cf0a082bb2577ce12a27f7303c1098",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-aarch64-apple-darwin-install_only.tar.gz",
            "ddd57774886a66a71283559cdd39bd2cbfd756cbd996b19b0b3bdb842a2a4a81",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "0d5edd43f3219744be8476f3ff2ab93af852647ff13e344622e83ca26634688d",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "ebe8ce15d3617e69219e27dc2aa39932aee9df8bf85d2ad46b157d309e3fa5d3",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-i686-pc-windows-msvc-install_only.tar.gz",
            "91e77e82e15c175bde41e6f1276d418e3bbb9378cb66d1b2fcb6f1b586375483",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "077c61230bc95673e95edd1e9cf43a8b2953470f8c83c50cd89bad08e530aa2b",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "cb7df2cb1ccdb9b5d24a7dc4de2b1183ea0344c9048da2393bc0bd8e5dc96cca",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-x86_64-apple-darwin-install_only.tar.gz",
            "9a5059869b715f8ae3c9fac215865275f1d0cbd075f506d7ca29faedda4e0533",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-x86_64-pc-windows-msvc-install_only.tar.gz",
            "cc0c5b42a227f594342c20a66c80f69940dd0f267c85292cc10040132a3161f1",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "f807820ac0f84735e8c4125d590093712252015398a1f4c7ff9795502f511ab4",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "ad5bd8e0eb95af34ba09c64a2aab1a5f3bdc0bf102501e0bb6a619c25583e55a",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.9.19%2B20240814-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "d21cf7bab25e2a8a1c873395bb7aa4f4b7ae533d2437d92f6496500c1e49625d",
        ),
    },
    PythonVersion("cpython", 3, 9, 18, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "579f9b68bbb3a915cbab9682e4d3c253bc96b0556b8a860982c49c25c61f974a",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-aarch64-apple-darwin-install_only.tar.gz",
            "2548f911a6e316575c303ba42bb51540dc9b47a9f76a06a2a37460d93b177aa2",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "93d7b15bf02a3191cfdee9d9d68bf2da782fc04cb142bcca6a4299fe524d9b37",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "e5bc5196baa603d635ee6b0cd141e359752ad3e8ea76127eb9141a3155c51200",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "904ff5d2f6402640e2b7e2b12075af0bd75b3e8685cc5248fd2a3cda3105d2a8",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "212d413ab6f854f588cf368fdd2aa140bb7c7ee930e3f7ac1002cba1e50e9685",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "146537b9b4a1baa672eed94373e149ca1ee339c4df121e8916d8436265e5245e",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-x86_64-apple-darwin-install_only.tar.gz",
            "171d8b472fce0295be0e28bb702c43d5a2a39feccb3e72efe620ac3843c3e402",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "a9bdbd728ed4c353a4157ecf74386117fb2a2769a9353f491c528371cfe7f6cd",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "924ed4f375ef73c73a725ef18ec6a72726456673d5a116f132f60860a25dd674",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "7de4b74bd7f5bbe897339cb692652471de28a97910abe4f8382f744baec551cf",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.9.18%2B20240224-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "0e5663025121186bd17d331538a44f48b41baff247891d014f3f962cbe2716b4",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.9.18%2B20230826-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "9e40a541b4eb6eb0a5e2f35724a18332aea91c61e18dec77ca40da5cf2496839",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.9.18%2B20230826-i686-unknown-linux-gnu-install_only.tar.gz",
            "10c422080317886057e968010495037ba65731ab7653bcaeabadf67a6fa5e99e",
        ),
    },
    PythonVersion("cpython", 3, 9, 17, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "2902e2a0add6d584999fa27896b721a359f7308404e936e80b01b07aa06e8f5e",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-aarch64-apple-darwin-install_only.tar.gz",
            "73dbe2d702210b566221da9265acc274ba15275c5d0d1fa327f44ad86cde9aa1",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "de2eab48ca487550258db38b38cb9372143283f757b3cf9ec522eb657e41a035",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "b77012ddaf7e0673e4aa4b1c5085275a06eee2d66f33442b5c54a12b62b96cbe",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "09f9d4bc66be5e0df2dfd1dc4742923e46c271f8f085178696c77073477aa0c1",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "ffac27bfb8bdf615d0fc6cbbe0becaa65b6ae73feec417919601497fce2be0ab",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "9984f59284048608f6734b032ff76e6bc3cb208e2235fdb511b0e478158fdb2b",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-i686-unknown-linux-gnu-install_only.tar.gz",
            "aed29a64c835444c2f1aff83c55b14123114d74c54d96493a0eabfdd8c6d012c",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "ba04f9813b78b61d60a27857949403a1b1dd8ac053e1f1aff72fe2689c238d3c",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-x86_64-apple-darwin-install_only.tar.gz",
            "dfe1bea92c94b9cb779288b0b06e39157c5ff7e465cdd24032ac147c2af485c0",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "9b9a1e21eff29dcf043cea38180cf8ca3604b90117d00062a7b31605d4157714",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "209983b8227e4755197dfed4f6887e45b6a133f61e7eb913c0a934b0d0c3e00f",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "cec2385699c047e77d32b93442417ab7d49c3e78c946cf586380dfe0b12a36dd",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.9.17%2B20230726-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "26c4a712b4b8e11ed5c027db5654eb12927c02da4857b777afb98f7a930ce637",
        ),
    },
    PythonVersion("cpython", 3, 9, 16, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "c86ed2bf3ff290af10f96183c53e2b29e954abb520806fbe01d3ef2f9d809a75",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-aarch64-apple-darwin-install_only.tar.gz",
            "c1de1d854717a6245f45262ef1bb17b09e2c587590e7e3f406593c143ff875bd",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "6c516ed541e7f84ba8b322aa15006082701456bba7c57e68e7263d702927a76d",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "f629b75ebfcafe9ceee2e796b7e4df5cf8dbd14f3c021afca078d159ab797acf",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "219532ffa49af88e3b90e9135cf3b6e1fa11cf165b03098fb9776a07af8ca6d0",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "d7994b5febb375bb131d028f98f4902ba308913c77095457ccd159b521e20c52",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "4df4cae277ba3ff8de7a16ef3b38f7214c2b0e4cc992f09505b859b0c94f2fd8",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-i686-unknown-linux-gnu-install_only.tar.gz",
            "ab0a14b3ae72bf48b94820e096e86b3cf3e05729862f768e109aa8318016c4f2",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "5809626ca7907c8ea397341f3d5eafb280ed5b19cc5622e57b14d9b4362eba50",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-x86_64-apple-darwin-install_only.tar.gz",
            "3abc4d5fbbc80f5f848f280927ac5d13de8dc03aabb6ae65d8247cbb68e6f6bf",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "cdabb47204e96ce7ea31fbd0b5ed586114dd7d8f8eddf60a509a7f70b48a1c5e",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "199c821505e287c004c3796ba9ac4bd129d7793e1d833e9a7672ed03bdb397d4",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "9fc89e1f3e1c03b4f5cd3c289f52e53a7c5fc8779113c2af5a10b19b2e8a2c2f",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230507/cpython-3.9.16%2B20230507-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "2b6e146234a4ef2a8946081fc3fbfffe0765b80b690425a49ebe40b47c33445b",
        ),
    },
    PythonVersion("cpython", 3, 9, 15, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "1799b97619572ad595cd6d309bbcc57606138a57f4e90af04e04ee31d187e22f",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-aarch64-apple-darwin-install_only.tar.gz",
            "64dc7e1013481c9864152c3dd806c41144c79d5e9cd3140e185c6a5060bdc9ab",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "4012279410b28c2688b4acfbc9189cdc8c81ef4c4f83c5e4532c39cb8685530e",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "52a8c0a67fb919f80962d992da1bddb511cdf92faf382701ce7673e10a8ff98f",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "0b81089247f258f244e9792daaa03675da6f58597daa6913e82f2679862238dd",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "a5ad2a6ace97d458ad7b2857fba519c5c332362442d88e2b23ed818f243b8a78",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "7c5d8e6a4255115e96c4b987b76c203ae9c7e6655b2d52c880680f13d2f1af36",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-i686-unknown-linux-gnu-install_only.tar.gz",
            "bf32a86c220e4d1690bb92b67653f20b8325808accd81bff03b5c30ae74e6444",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "50fd795eac55c4485e2fefbb8e7b365461817733c45becb50a7480a243e6000e",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-x86_64-apple-darwin-install_only.tar.gz",
            "f2bcade6fc976c472f18f2b3204d67202d43ae55cf6f9e670f95e488f780da08",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "022daacab215679b87f0d200d08b9068a721605fa4721ebeda38220fc641ccf6",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "d0f3ce1748a51779eedf155aea617c39426e3f7bfd93b4876cb172576b6e8bda",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "b6860b9872f361af78021dd2e1fe7edfe821963deab91b9a813d12d706288d3d",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.9.15%2B20221106-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "cdc3a4cfddcd63b6cebdd75b14970e02d8ef0ac5be4d350e57ab5df56c19e85e",
        ),
    },
    PythonVersion("cpython", 3, 9, 14, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "6b9d2ff724aff88a4d0790c86f2e5d17037736f35a796e71732624191ddd6e38",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-aarch64-apple-darwin-install_only.tar.gz",
            "e38df7f230979ce6c53a5bafb3a81287838e5f3892c40cd1b98a0c961c444713",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "b099375504383b3a30af02dcf3a9ce01b0e6fecba5b3a8729b4a0a374fee7984",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "fe538201559ca37f44cd5f66c42a65fe7272cb4f1f63edd698b6f306771db1e9",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "f3526e8416be86ff9091750ebc7388d6726acf32cc5ab0e6a60c67c6aacb2569",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "fae990eb312314102408cb0c0453dae670f0eb468f4cbf3e72327ceaa1276b46",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "612031ffd5b6dee7f4fe205afeee62a996bbd8df338ae7d0f3731a825aee04fb",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-i686-unknown-linux-gnu-install_only.tar.gz",
            "3af1c255110c2f42ed0b7957502c92edf8b5c5e6fc5f699a2475bf8a560325c0",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "186155e19b63da3248347415f888fbcf982c7587f6f927922ca243ae3f23ed2f",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-x86_64-apple-darwin-install_only.tar.gz",
            "b7d3a1f4b57e9350571ccee49c82f503133de0d113a2dbaebc8ccf108fb3fe1b",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "f111c3c129f4a5a171d25350ce58dad4c7e58fbe664e9b4f7c275345c9fe18a6",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "49f27a3a18b4c2d765b0656c6529378a20b3e37fdb0aca9490576ff7a67243a9",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "7f88ff09b2b57c19f4262026b0919aca59558971838093c63b68dfce7834e84d",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.9.14%2B20221002-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "e63d0c00a499e0202ba7a0f53ce69fca6d30237af39af9bc3c76bce6c7bf14d7",
        ),
    },
    PythonVersion("cpython", 3, 9, 13, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "8612e9328663c0747d1eae36b218d11c2fbc53c39ec7512c7ad6b1b57374a5dc",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-aarch64-apple-darwin-install_only.tar.gz",
            "d9603edc296a2dcbc59d7ada780fd12527f05c3e0b99f7545112daf11636d6e5",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "e27d88c3c3424a3694f9f111dc4e881c3925aa5d9ec60ec8395a82da2d7c2f31",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "80415aac1b96255b9211f6a4c300f31e9940c7e07a23d0dec12b53aa52c0d25e",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "90e3879382f06fea3ba6d477f0c2a434a1e14cd83d174e1c7b87e2f22bc2e748",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "3860abee418825c6a33f76fe88773fb05eb4bc724d246f1af063106d9ea3f999",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "066d4722bcc75fb16000afd745b11fb5c02847471695c67db633918969e3936b",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-i686-unknown-linux-gnu-install_only.tar.gz",
            "efcc8fef0d498afe576ab209fee001fda3b552de1a85f621f2602787aa6cf3d4",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "16d21a6e62c19c574a4a225961e80966449095a8eb2c4150905e30d4e807cf86",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-x86_64-apple-darwin-install_only.tar.gz",
            "9540a7efb7c8a54a48aff1cb9480e49588d9c0a3f934ad53f5b167338174afa3",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "b538127025a467c64b3351babca2e4d2ea7bdfb7867d5febb3529c34456cdcd4",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "6ef2b164cae483c61da30fb6d245762b8d6d91346d66cb421989d6d1462e5a48",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "e586b6fef3943adff4e74fbc3fe276dfbca12e9d883e273ed0c8d781b24d7d6e",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.9.13%2B20220802-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "ce1cfca2715e7e646dd618a8cb9baff93000e345ccc979b801fc6ccde7ce97df",
        ),
    },
    PythonVersion("cpython", 3, 9, 12, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "b3d09b3c12295e893ee8f2cb60e8af94d8a21fc5c65016282925220f5270b85b",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-aarch64-apple-darwin-install_only.tar.gz",
            "8dee06c07cc6429df34b6abe091a4684a86f7cec76f5d1ccc1c3ce2bd11168df",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "0749e4f8169b45051c440c81c17449549710d0e5821d4fdb5170b704ddd165c4",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "2ee1426c181e65133e57dc55c6a685cb1fb5e63ef02d684b8a667d5c031c4203",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "8b7e440137bfa349a008641a75a2b1fd8ae22d290731778a144878a59a721c51",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "361b8fa66d6b5d5623fd5e64af29cf220a693ba86d031bf7ce2b61e1ea50f568",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "4a32d5f827e9c1fbed68e51974d78f090ccdd8c83f777a2c9f80644a96d53c3f",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-i686-unknown-linux-gnu-install_only.tar.gz",
            "233e1a9626d9fe13baac8de3689df48401d0ad5da1c2f134ad57d8e3e878a1a5",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "825970ae30ae7a30a5b039aa25f1b965e2d1fe046e196e61fa2a3af8fef8c5d9",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-x86_64-apple-darwin-install_only.tar.gz",
            "2453ba7f76b3df3310353b48c881d6cff622ba06e30d2b6ae91588b2bc9e481a",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "3024147fd987d9e1b064a3d94932178ff8e0fe98cfea955704213c0762fee8df",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "c49f8b07e9c4dcfd7a5b55c131e882a4ebdf9f37fef1c7820c3ce9eb23bab8ab",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "9af4ad8e87d1d24352163d519df44f652efefe018b8c7b48ca57604054950abe",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220502/cpython-3.9.12%2B20220502-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "ccca12f698b3b810d79c52f007078f520d588232a36bc12ede944ec3ea417816",
        ),
    },
    PythonVersion("cpython", 3, 9, 11, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "6d9f20607a20e2cc5ad1428f7366832dc68403fc15f2e4f195817187e7b6dbbf",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-aarch64-apple-darwin-install_only.tar.gz",
            "cf92a28f98c8d884df0937bf19d5f1a40caa25a6a211a237b7e9b592b2b71c2b",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "e540f92f78cc84a52a77ce621c3da5a427367205884ab4210e763bc7fdaf889c",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "0e50f099409c5e651b5fddd16124af1d830d11653e786a93c28e5b8f8aa470c4",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "ceac8729b285a8c8e861176dd2dadd7f8e7e26d8f64cac6c6226a14d2252cd4c",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "f06338422e7e3ad25d0cd61864bdb36d565d46440dd363cbb98821d388ed377a",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "aeb50fcc54214780244dd64c0d66bf5dec30db075c999cf2c5a58134f8d21c33",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-i686-unknown-linux-gnu-install_only.tar.gz",
            "75ac727631eab002bd120246197a8235145cb90687be181f7a52de6f41d44d34",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "35e649618e7e602778e72b91c9c50c97d01a0c3509d16225a1f41dd0fd6575f0",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-x86_64-apple-darwin-install_only.tar.gz",
            "43889d1a424c84fb155e1619f062adb6984fbde80b6043611790f22bcbeec300",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "0c529a511f7a03908fc126c4a8467b47e24a4d98812147e8e786cf59e86febf0",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "1fe3c519d43737dc7743aec43f72735e1429c79e06e3901b21bad67b642f1a10",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "49dfa5cb99d4f71657dc651ad68d0fce7cc011beb59499141138ef062bd62b49",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220318/cpython-3.9.11%2B20220318-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "0429d5ceb095d5e24c292bf1a39208b88ae236a680ef8fa3e1830e3a1a7e8882",
        ),
    },
    PythonVersion("cpython", 3, 9, 10, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "ba1b63600ed8d9f3b8d739657bd8e7f5ca167de29a1a58d04b2cd9940b289464",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-aarch64-apple-darwin-install_only.tar.gz",
            "ad66c2a3e7263147e046a32694de7b897a46fb0124409d29d3a93ede631c8aee",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "a40dc3f12bbcaeb487d2ece8c5415f94f3856b400f78202b6055cd514c5e9a24",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "12dd1f125762f47975990ec744532a1cf3db74ad60f4dfb476ca42deb7f78ca4",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "56c0342a9af0412676e89cdf7b52ac76037031786b3f5c40942b8b82d366c96f",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "7f3ca15f89775f76a32e6ea9b2c9778ebf0cde753c5973d4493959e75dd92488",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "218a79ef09d599d95a04819311ee27ab0fd34dd80d3722347003fec0139dca7b",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-i686-unknown-linux-gnu-install_only.tar.gz",
            "37ba43845c3df9ba012d69121ad29ea7f21ea2f5994a155007cf1560d74ce503",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "ef2f090ff920708b4b9aa5d6adf0dc930c09a4bf638d71e6883091f9e629193d",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-x86_64-apple-darwin-install_only.tar.gz",
            "fdaf594142446029e314a9beb91f1ac75af866320b50b8b968181e592550cd68",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "c145d9d8143ce163670af124b623d7a2405143a3708b033b4d33eed355e61b24",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "56b2738599131d03b39b914ea0597862fd9096e5e64816bf19466bf026e74f0c",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "de0a1b11f56cd6acdbc4b369a023377fd830946726f3abbbce8fc11dcb56cac0",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.9.10%2B20220227-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "455089cc576bd9a58db45e919d1fc867ecdbb0208067dffc845cc9bbf0701b70",
        ),
    },
    PythonVersion("cpython", 3, 9, 7, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.9.7-aarch64-apple-darwin-pgo%2Blto-20211017T1616.tar.zst",
            None,
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.9.7-aarch64-apple-darwin-install_only-20211017T1616.tar.gz",
            None,
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.9.7-aarch64-unknown-linux-gnu-lto-20211017T1616.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.9.7-i686-pc-windows-msvc-shared-pgo-20211017T1616.tar.zst",
            None,
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.9.7-i686-unknown-linux-gnu-pgo%2Blto-20211017T1616.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.9.7-x86_64-apple-darwin-pgo%2Blto-20211017T1616.tar.zst",
            None,
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.9.7-x86_64-apple-darwin-install_only-20211017T1616.tar.gz",
            None,
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.9.7-x86_64-pc-windows-msvc-shared-install_only-20211017T1616.tar.gz",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.9.7-x86_64-pc-windows-msvc-shared-pgo-20211017T1616.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.9.7-x86_64-unknown-linux-gnu-pgo%2Blto-20211017T1616.tar.zst",
            None,
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20211017/cpython-3.9.7-x86_64-unknown-linux-gnu-install_only-20211017T1616.tar.gz",
            None,
        ),
    },
    PythonVersion("cpython", 3, 9, 6, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.9.6-aarch64-apple-darwin-pgo%2Blto-20210724T1424.tar.zst",
            None,
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.9.6-aarch64-apple-darwin-install_only-20210724T1424.tar.gz",
            None,
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.9.6-aarch64-unknown-linux-gnu-lto-20210724T1424.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.9.6-i686-pc-windows-msvc-shared-pgo-20210724T1424.tar.zst",
            None,
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.9.6-i686-unknown-linux-gnu-pgo%2Blto-20210724T1424.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.9.6-x86_64-apple-darwin-pgo%2Blto-20210724T1424.tar.zst",
            None,
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.9.6-x86_64-apple-darwin-install_only-20210724T1424.tar.gz",
            None,
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.9.6-x86_64-pc-windows-msvc-shared-install_only-20210724T1424.tar.gz",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.9.6-x86_64-pc-windows-msvc-shared-pgo-20210724T1424.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.9.6-x86_64-unknown-linux-gnu-pgo%2Blto-20210724T1424.tar.zst",
            None,
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.9.6-x86_64-unknown-linux-gnu-install_only-20210724T1424.tar.gz",
            None,
        ),
    },
    PythonVersion("cpython", 3, 9, 5, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210506/cpython-3.9.5-aarch64-apple-darwin-pgo%2Blto-20210506T0943.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210506/cpython-3.9.5-i686-pc-windows-msvc-shared-pgo-20210506T0943.tar.zst",
            None,
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210506/cpython-3.9.5-i686-unknown-linux-gnu-pgo%2Blto-20210506T0943.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210506/cpython-3.9.5-x86_64-apple-darwin-pgo%2Blto-20210506T0943.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210506/cpython-3.9.5-x86_64-pc-windows-msvc-shared-pgo-20210506T0943.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210506/cpython-3.9.5-x86_64-unknown-linux-gnu-pgo%2Blto-20210506T0943.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 9, 4, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210415/cpython-3.9.4-aarch64-apple-darwin-pgo%2Blto-20210414T1515.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210415/cpython-3.9.4-i686-pc-windows-msvc-shared-pgo-20210414T1515.tar.zst",
            None,
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210415/cpython-3.9.4-i686-unknown-linux-gnu-pgo%2Blto-20210414T1515.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210415/cpython-3.9.4-x86_64-apple-darwin-pgo%2Blto-20210414T1515.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210415/cpython-3.9.4-x86_64-pc-windows-msvc-shared-pgo-20210414T1515.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210415/cpython-3.9.4-x86_64-unknown-linux-gnu-pgo%2Blto-20210414T1515.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 9, 3, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210414/cpython-3.9.3-aarch64-apple-darwin-pgo%2Blto-20210413T2055.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210414/cpython-3.9.3-i686-pc-windows-msvc-shared-pgo-20210413T2055.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210414/cpython-3.9.3-x86_64-apple-darwin-pgo%2Blto-20210413T2055.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210414/cpython-3.9.3-x86_64-pc-windows-msvc-shared-pgo-20210413T2055.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210414/cpython-3.9.3-x86_64-unknown-linux-gnu-pgo%2Blto-20210413T2055.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 9, 2, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210327/cpython-3.9.2-aarch64-apple-darwin-pgo%2Blto-20210327T1202.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210327/cpython-3.9.2-i686-pc-windows-msvc-shared-pgo-20210327T1202.tar.zst",
            None,
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210327/cpython-3.9.2-i686-unknown-linux-gnu-pgo%2Blto-20210327T1202.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210327/cpython-3.9.2-x86_64-apple-darwin-pgo%2Blto-20210327T1202.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210327/cpython-3.9.2-x86_64-pc-windows-msvc-shared-pgo-20210327T1202.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210327/cpython-3.9.2-x86_64-unknown-linux-gnu-pgo%2Blto-20210327T1202.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 9, 1, False): {
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210103/cpython-3.9.1-i686-pc-windows-msvc-shared-pgo-20210103T1125.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210103/cpython-3.9.1-x86_64-apple-darwin-pgo-20210103T1125.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210103/cpython-3.9.1-x86_64-pc-windows-msvc-shared-pgo-20210103T1125.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210103/cpython-3.9.1-x86_64-unknown-linux-gnu-pgo-20210103T1125.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 9, 0, False): {
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20201020/cpython-3.9.0-i686-pc-windows-msvc-shared-pgo-20201021T0245.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20201020/cpython-3.9.0-x86_64-apple-darwin-pgo-20201020T0626.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20201020/cpython-3.9.0-x86_64-pc-windows-msvc-shared-pgo-20201021T0245.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20201020/cpython-3.9.0-x86_64-unknown-linux-gnu-pgo-20201020T0627.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 8, 20, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "68f99a2a8aa8d4c319f095a4d8ac1ffffd24ef31827ab81d290f2f0ae3f3a424",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-aarch64-apple-darwin-install_only.tar.gz",
            "2ddfc04bdb3e240f30fb782fa1deec6323799d0e857e0b63fa299218658fd3d4",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "05ed8e6403ed0692394ad0804e79bbc847d5d68cb3ad57b46b29065b7ad4a53b",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "9d8798f9e79e0fc0f36fcb95bfa28a1023407d51a8ea5944b4da711f1f75f1ed",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-i686-pc-windows-msvc-install_only.tar.gz",
            "06988218600fe3e2c02c3182d5629a1d4e596b3771ab32a9e99756c5904b5fac",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "c19216a5e3040abd2d315e71dac607c991897f82d6b8c22860c9f2329c8bd6a3",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "0b619b6d156244f1cbd1b91f2bf838cbcd69eeed4fb7edfcf4da4c6c526adc49",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-x86_64-apple-darwin-install_only.tar.gz",
            "68d060cd373255d2ca5b8b3441363d5aa7cc45b0c11bbccf52b1717c2b5aa8bb",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-x86_64-pc-windows-msvc-install_only.tar.gz",
            "41b6709fec9c56419b7de1940d1f87fa62045aff81734480672dcb807eedc47e",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "b84dd2228d36fc2381573af1002d0a9cb244a2a52c7c470c2fafec1f855459c3",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "25b45589eb04dfb6874e597f017b2ff180120330ca255317751238f682f34091",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20241002/cpython-3.8.20%2B20241002-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "285e141c36f88b2e9357654c5f77d1f8fb29cc25132698fe35bb30d787f38e87",
        ),
    },
    PythonVersion("cpython", 3, 8, 19, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "43f3b6d7816448b44f86d2186dba1b7418533a3f4a37d07e5075bb934bcfba76",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-aarch64-apple-darwin-install_only.tar.gz",
            "f01a9d293e2bff8c4c62478d826a8bb46a197fabe21b26cb4db7a098e23dc9f1",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "d57b944f770579a2c24b34880843318135ddc816ccb67d9a7022b2c00b2c897e",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "cfa765bf74d034ec45624e3a56f9560d09ca2d4f306aeabaa165c96a1d93c7cb",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-i686-pc-windows-msvc-install_only.tar.gz",
            "f64096a3897874cf83fb3628816cdafa3657c996d9d379267b85260f35a35e5c",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "8f69364b8421a51a71da5e897d4f59d0659f18b354896d28c79756de6011c21c",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "04c2aca9f8ecceb4f72775d104586608489a016439e4a361d967748bde4949fd",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-x86_64-apple-darwin-install_only.tar.gz",
            "e13c7afca48e8ef64b38261567bb3b496276d097435d1404636f335447c992c3",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-x86_64-pc-windows-msvc-install_only.tar.gz",
            "5775cfcfa009c47b6d9b7029d349b5c5b7acc03658319a768e70c5387465a864",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "31ff501b345a4054a4eb6a886b4d2799bd11b15afe7338dc2f2e5f2a123f4dba",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "adbe33a5f9a6d3cd05ef90ca2aed7db8d0002492cfdfe81c24cabf6e6e6aacee",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240814/cpython-3.8.19%2B20240814-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "9ebb4d3ff993e977c5f2c043369024be8429447cee67a16e7d4a84f03064116a",
        ),
    },
    PythonVersion("cpython", 3, 8, 18, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "c732c068cddcd6a008c1d6d8e35802f5bdc7323bd2eb64e77210d3d5fe4740c2",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-aarch64-apple-darwin-install_only.tar.gz",
            "4d493a1792bf211f37f98404cc1468f09bd781adc2602dea0df82ad264c11abc",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "df66801678a5f4accee67784aff058f283fd52e42898527b7ff0e1cbc3e50e8c",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "6588c9eed93833d9483d01fe40ac8935f691a1af8e583d404ec7666631b52487",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "c24f9c9e8638cff0ce6aa808a57cc5f22009bc33e3bcf410a726b79d7c5545fe",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "9f94c7b54b97116cd308e73cda0b7a7b7fff4515932c5cbba18eeae9ec798351",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "4d4b65dd821ce13dcf6dfea3ad5c2d4c3d3a8c2b7dd49fc35c1d79f66238e89b",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-x86_64-apple-darwin-install_only.tar.gz",
            "7d2cd8d289d5e3cdd0a8c06c028c7c621d3d00ce44b7e2f08c1724ae0471c626",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "dba923ee5df8f99db04f599e826be92880746c02247c8d8e4d955d4bc711af11",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "c63abd9365a13196eb9f65db864f95b85c1f90b770d218c1acd104e6b48a99d3",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "7ede28a7119056c24ea51766ac3cd9d3c5d579d3db133e02051b4bcb300507e9",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20240224/cpython-3.8.18%2B20240224-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "5ae36825492372554c02708bdd26b8dcd57e3dbf34b3d6d599ad91d93540b2b7",
        ),
    },
    PythonVersion("cpython", 3, 8, 17, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "d08a542bed35fc74ac6e8f6884c8aa29a77ff2f4ed04a06dcf91578dea622f9a",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-aarch64-apple-darwin-install_only.tar.gz",
            "c6f7a130d0044a78e39648f4dae56dcff5a41eba91888a99f6e560507162e6a1",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "efdf69695af469da13f86d5be23556fee6c03f417f8810fca55307a63aabf08d",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "9f6d585091fe26906ff1dbb80437a3fe37a1e3db34d6ecc0098f3d6a78356682",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "cb6af626ba811044e9c5ee09140a6920565d2b1b237a11886b96354a9fcc242e",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "0931d8ca0e060c6ac1dfcf6bb9b6dea0ac3a9d95daf7906a88128045f4464bf8",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "aaf4b15bdc35674dbe25d4538c9e75e243796a0cc8841fd31d7bbbee6703342a",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-i686-unknown-linux-gnu-install_only.tar.gz",
            "e580fdd923bbae612334559dc58bd5fd13cce53b769294d63bc88e7c6662f7d9",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "2c4925f5cf37d498e0d8cfe7b10591cc5f0cd80d2582f566b12006e6f96958b1",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-x86_64-apple-darwin-install_only.tar.gz",
            "155b06821607bae1a58ecc60a7d036b358c766f19e493b8876190765c883a5c2",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "6428e1b4e0b4482d390828de7d4c82815257443416cb786abe10cb2466ca68cd",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "68c7d03de5283c4812f2706c797b2139999a28cec647bc662d1459a922059318",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "4bfe1055dee03d4357b3dca5b334df3076b8aab066cdd84596199b9712ee3632",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230826/cpython-3.8.17%2B20230826-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "8d3e1826c0bb7821ec63288038644808a2d45553245af106c685ef5892fabcd8",
        ),
    },
    PythonVersion("cpython", 3, 8, 16, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "bfc91d0a1d6d6dfaa5a31c925aa6adae82bd1ae5eb17813a9f0a50bf9d3e6305",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-aarch64-apple-darwin-install_only.tar.gz",
            "7e484eb6de40d6f6bdfd5099eaa9647f65e45fb6d846ccfc56b1cb1e38b5ab02",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "62c3e7b417a9c11fb7d251ee6f763c7dd2ae681017a82686122a8167f1b8c081",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "9c6615931fd1045bf9f2148aa7dd9ce1ece8575ed68a5483a0b615322a43d54c",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "77466f93ef5b030cf13d0446067089b0ce0d415cc6d1702655bdbb12a8c18c97",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "5de953621402c11cc7db65ba15d45779e838d7ce78e7aa8d43c7d78fff177f13",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "e8d832f16548e199e7c622eec9e06f746ba9dbbdf562dac8810c4e64e1f5115a",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-i686-unknown-linux-gnu-install_only.tar.gz",
            "1260fd6af34104bbd57489175e6f7bfea76d4bd06a242a0f8e20e390e870b227",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "21c0f4a0fa6ee518b9f2f1901c9667e3baf45d9f84235408b7ca50499d19f56d",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-x86_64-apple-darwin-install_only.tar.gz",
            "28506e509646c11cb2f57a7203bd1b08b6e8e5b159ae308bd5bb93b0d334bdaf",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "120b3312fa79bac2ace45641171c2bc590c4e4462d7ad124d64597e124a36ae7",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "6316713c2dcb30127b38ced249fa9608830a33459580b71275a935aaa8cd5d5f",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "446a1f600698167a3e70448787f61dd8b1e6fb8f50f50558c901a0f4d3c7a6d6",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20230726/cpython-3.8.16%2B20230726-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "b1f1502c3a13b899724dbd32bd77a973fa9733b932c5700d747fe33d5de9ac4f",
        ),
    },
    PythonVersion("cpython", 3, 8, 15, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "fc0f944e6f01ed649f79c873af1c317db61d2136b82081b4d7cbb7755f878035",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-aarch64-apple-darwin-install_only.tar.gz",
            "1e0a92d1a4f5e6d4a99f86b1cbf9773d703fe7fd032590f3e9c285c7a5eeb00a",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "3a4975f1b0c196c98b4867ad41d2f1ba211b52dc6a2965c56acbb00eb7f69aa7",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "886ab33ced13c84bf59ce8ff79eba6448365bfcafea1bf415bd1d75e21b690aa",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "318c059324b84b5d7685bcd0874698799d9e3689b51dbcf596e7a47a39a3d49a",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "98bb2315c3567316c30b060d613c8d6067b368b64f08ef8fe6196341637c1d78",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "f76c0d13f600e819696035851ec47cf5a233cf053d2de85fbd8e5e12a8146f5f",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-i686-unknown-linux-gnu-install_only.tar.gz",
            "3bc1f49147913d93cea9cbb753fbaae90b86f1ee979f975c4712a35f02cbd86b",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "e4fd2fa2255295fbdcfadb8b48014fa80810305eccb246d355880aabb45cbe93",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-x86_64-apple-darwin-install_only.tar.gz",
            "70b57f28c2b5e1e3dd89f0d30edd5bc414e8b20195766cf328e1b26bed7890e1",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "2fdc3fa1c95f982179bbbaedae2b328197658638799b6dcb63f9f494b0de59e2",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "59beac5610e6da0848ebaccd72f91f6aaaeed65ef59606d006af909e9e79beba",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "1fd71062d9b7d632af202972c4488fa9c2255d2ef072b80766ab059b37473ea5",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221106/cpython-3.8.15%2B20221106-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "e47edfb2ceaf43fc699e20c179ec428b6f3e497cf8e2dcd8e9c936d4b96b1e56",
        ),
    },
    PythonVersion("cpython", 3, 8, 14, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "d17a3fcc161345efa2ec0b4ab9c9ed6c139d29128f2e34bb636338a484aa7b72",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-aarch64-apple-darwin-install_only.tar.gz",
            "6c17f6dcda59de5d8eee922ef7eede403a540dae05423ef2c2a042d8d4f22467",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "650821c45386e7727b6e682620007d2532d9ee599b2caf4b4356575bee3c77a0",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "c45e42deee43e3ebc4ca5b019c37d8ae25fb5b5f1ba5f602098a81b99d2bc804",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "a0730f3a9e60581f02bdb852953fbb52cf98e8431259fa39cb668a060bd002a0",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "e43f7a5044eac91e95df59fd08bf96f13245898876fc2afd90a081cfcd847e35",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "adb5a08f8dd700bc2d8260226354137349939e9bc5ccfdb8c16493e97b593a19",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-i686-unknown-linux-gnu-install_only.tar.gz",
            "d01d813939ad549ca253c52e5b8361b4490cc5c8cbda00ab6e0c524565153e2b",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "62edfea77b42e87ca2d85c482319211cd2dd68d55ba85c99f1834f7b64a60133",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-x86_64-apple-darwin-install_only.tar.gz",
            "3ed4db8d0308c584196d97c629058ea69bbd8b7f9a034cf8c2c701ebb286c091",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "1af39953b4c8324ed0608e316bc763006f27e76643155d92eae18e4db6fc162f",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "6986b3e6edf7b37f96ea940b7ccba7b767ed3ea9b3faec2a2a60e5b2c4443314",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "5ca1c591ffb019fad3978018f68d69d4b6c73ce629fb7e42bc2c594cd8344d4f",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20221002/cpython-3.8.14%2B20221002-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "4eb53bce831bf52682067579c09ccaccb6524dd44bd4b8047454c69b4817f4f0",
        ),
    },
    PythonVersion("cpython", 3, 8, 13, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "a204e5f9e1566bdc170b163300a29fc9580d5c65cd6e896caf6500cd64471373",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-aarch64-apple-darwin-install_only.tar.gz",
            "ae4131253d890b013171cb5f7b03cadc585ae263719506f7b7e063a7cf6fde76",
        ),
        ("linux", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-aarch64-unknown-linux-gnu-lto-full.tar.zst",
            "ad2b859fb502491f72f8d74ed3410bfb78a8886f8a1baa6908faea6128d91265",
        ),
        ("linux", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-aarch64-unknown-linux-gnu-install_only.tar.gz",
            "8dc7814bf3425bbf78c6e6e5a6529ded6ae463fa6a4b79c025b343bae4fd955a",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "a50668d4c5fbcb374d3ca93ee18db910bc3b462693db073669f31e6da993abf9",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "5630739d1c6fcfbf90311d236c5e46314fc4b439364429bee12d0ffc95e134fb",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "9191ac9858eddfc727fa5ebadc654a57a719ac96b9dee4e1e48e6498a27499f4",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-i686-unknown-linux-gnu-install_only.tar.gz",
            "9485599ad9053dfba08c91854717272e95b7c81e0d099d9c51a46fc5a095ccb4",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "f706a62de8582bf84b8b693c993314cd786f3e78639892cfd9a7283a526696f9",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-x86_64-apple-darwin-install_only.tar.gz",
            "cd6e7c0a27daf7df00f6882eaba01490dd963f698e99aeee9706877333e0df69",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "f20643f1b3e263a56287319aea5c3888530c09ad9de3a5629b1a5d207807e6b9",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "c36b703b8b806a047ba71e5e85734ac78d204d3a2b7ebc2efcdc7d4af6f6c263",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "31c98d8329746c19739558f164e6374a2cd9c5c93c9e213d2548c993566a593c",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220802/cpython-3.8.13%2B20220802-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "fb566629ccb5f76ef56d275a3f8017d683f1c20c5beb5d5f38b155ed11e16187",
        ),
    },
    PythonVersion("cpython", 3, 8, 12, False): {
        ("macos", "aarch64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-aarch64-apple-darwin-pgo%2Blto-full.tar.zst",
            "386f667f8d49b6c34aee1910cdc0b5b41883f9406f98e7d59a3753990b1cdbac",
        ),
        ("macos", "aarch64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-aarch64-apple-darwin-install_only.tar.gz",
            "f9a3cbb81e0463d6615125964762d133387d561b226a30199f5b039b20f1d944",
        ),
        ("windows", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-i686-pc-windows-msvc-shared-install_only.tar.gz",
            "aaa75b9115af73dc3daf7db050ed4f60fd67d2a23ebab30670f18fb8cfa71f33",
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-i686-pc-windows-msvc-shared-pgo-full.tar.zst",
            "3e2e6c7de78b1924aad37904fed7bfbac6efa2bef05348e9be92180b2f2b1ae1",
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-i686-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "61024acdfe5aef07ba4246ea07dba9962770ec1f3d137c54835c0e5b6e040149",
        ),
        ("linux", "x86", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-i686-unknown-linux-gnu-install_only.tar.gz",
            "fcb2033f01a2b10a51be68c9a1b4c7d7759b582f58a503371fe67ab59987b418",
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-x86_64-apple-darwin-pgo%2Blto-full.tar.zst",
            "cf614d96e2001d526061b3ce0569c79057fd0074ace472ff4f5f601262e08cdb",
        ),
        ("macos", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-x86_64-apple-darwin-install_only.tar.gz",
            "f323fbc558035c13a85ce2267d0fad9e89282268ecb810e364fff1d0a079d525",
        ),
        ("windows", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-x86_64-pc-windows-msvc-shared-install_only.tar.gz",
            "4658e08a00d60b1e01559b74d58ff4dd04da6df935d55f6268a15d6d0a679d74",
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-x86_64-pc-windows-msvc-shared-pgo-full.tar.zst",
            "33f278416ba8074f2ca6d7f8c17b311b60537c9e6431fd47948784c2a78ea227",
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-x86_64-unknown-linux-gnu-pgo%2Blto-full.tar.zst",
            "a014cf132a642a5d585f37da0c56f7e6672699811726af18e8905d652b261a3f",
        ),
        ("linux", "x86_64", True): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20220227/cpython-3.8.12%2B20220227-x86_64-unknown-linux-gnu-install_only.tar.gz",
            "5be9c6d61e238b90dfd94755051c0d3a2d8023ebffdb4b0fa4e8fedd09a6cab6",
        ),
    },
    PythonVersion("cpython", 3, 8, 11, False): {
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.8.11-i686-pc-windows-msvc-shared-pgo-20210724T1424.tar.zst",
            None,
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.8.11-i686-unknown-linux-gnu-pgo%2Blto-20210724T1424.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.8.11-x86_64-apple-darwin-pgo%2Blto-20210724T1424.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.8.11-x86_64-pc-windows-msvc-shared-pgo-20210724T1424.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210724/cpython-3.8.11-x86_64-unknown-linux-gnu-pgo%2Blto-20210724T1424.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 8, 10, False): {
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210506/cpython-3.8.10-i686-pc-windows-msvc-shared-pgo-20210506T0943.tar.zst",
            None,
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210506/cpython-3.8.10-i686-unknown-linux-gnu-pgo%2Blto-20210506T0943.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210506/cpython-3.8.10-x86_64-apple-darwin-pgo%2Blto-20210506T0943.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210506/cpython-3.8.10-x86_64-pc-windows-msvc-shared-pgo-20210506T0943.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210506/cpython-3.8.10-x86_64-unknown-linux-gnu-pgo%2Blto-20210506T0943.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 8, 9, False): {
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210415/cpython-3.8.9-i686-pc-windows-msvc-shared-pgo-20210414T1515.tar.zst",
            None,
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210415/cpython-3.8.9-i686-unknown-linux-gnu-pgo%2Blto-20210414T1515.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210415/cpython-3.8.9-x86_64-apple-darwin-pgo%2Blto-20210414T1515.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210415/cpython-3.8.9-x86_64-pc-windows-msvc-shared-pgo-20210414T1515.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210415/cpython-3.8.9-x86_64-unknown-linux-gnu-pgo%2Blto-20210414T1515.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 8, 8, False): {
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210327/cpython-3.8.8-i686-pc-windows-msvc-shared-pgo-20210327T1202.tar.zst",
            None,
        ),
        ("linux", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210327/cpython-3.8.8-i686-unknown-linux-gnu-pgo%2Blto-20210327T1202.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210327/cpython-3.8.8-x86_64-apple-darwin-pgo%2Blto-20210327T1202.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210327/cpython-3.8.8-x86_64-pc-windows-msvc-shared-pgo-20210327T1202.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210327/cpython-3.8.8-x86_64-unknown-linux-gnu-pgo%2Blto-20210327T1202.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 8, 7, False): {
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210103/cpython-3.8.7-i686-pc-windows-msvc-shared-pgo-20210103T1125.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210103/cpython-3.8.7-x86_64-apple-darwin-pgo-20210103T1125.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210103/cpython-3.8.7-x86_64-pc-windows-msvc-shared-pgo-20210103T1125.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20210103/cpython-3.8.7-x86_64-unknown-linux-gnu-pgo-20210103T1125.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 8, 6, False): {
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20201020/cpython-3.8.6-i686-pc-windows-msvc-shared-pgo-20201021T0233.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20201020/cpython-3.8.6-x86_64-apple-darwin-pgo-20201020T0626.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20201020/cpython-3.8.6-x86_64-pc-windows-msvc-shared-pgo-20201021T0232.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20201020/cpython-3.8.6-x86_64-unknown-linux-gnu-pgo-20201020T0627.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 8, 5, False): {
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200830/cpython-3.8.5-i686-pc-windows-msvc-shared-pgo-20200830T2311.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200830/cpython-3.8.5-x86_64-pc-windows-msvc-shared-pgo-20200830T2254.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200823/cpython-3.8.5-x86_64-apple-darwin-pgo-20200823T2228.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200822/cpython-3.8.5-x86_64-unknown-linux-gnu-pgo-20200823T0036.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 8, 3, False): {
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200530/cpython-3.8.3-x86_64-apple-darwin-pgo-20200530T1845.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200517/cpython-3.8.3-i686-pc-windows-msvc-shared-pgo-20200518T0154.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200517/cpython-3.8.3-x86_64-pc-windows-msvc-shared-pgo-20200517T2207.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200517/cpython-3.8.3-x86_64-unknown-linux-gnu-pgo-20200518T0040.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 8, 2, False): {
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200418/cpython-3.8.2-i686-pc-windows-msvc-shared-pgo-20200418T2315.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200418/cpython-3.8.2-x86_64-apple-darwin-pgo-20200418T2238.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200418/cpython-3.8.2-x86_64-pc-windows-msvc-shared-pgo-20200418T2315.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200418/cpython-3.8.2-x86_64-unknown-linux-gnu-pgo-20200418T2243.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 7, 9, False): {
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200823/cpython-3.7.9-x86_64-apple-darwin-pgo-20200823T2228.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200822/cpython-3.7.9-i686-pc-windows-msvc-shared-pgo-20200823T0159.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200822/cpython-3.7.9-x86_64-pc-windows-msvc-shared-pgo-20200823T0118.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200822/cpython-3.7.9-x86_64-unknown-linux-gnu-pgo-20200823T0036.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 7, 7, False): {
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200530/cpython-3.7.7-x86_64-apple-darwin-pgo-20200530T1845.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200517/cpython-3.7.7-i686-pc-windows-msvc-shared-pgo-20200517T2153.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200517/cpython-3.7.7-x86_64-pc-windows-msvc-shared-pgo-20200517T2128.tar.zst",
            None,
        ),
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200517/cpython-3.7.7-x86_64-unknown-linux-gnu-pgo-20200518T0040.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 7, 6, False): {
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200216/cpython-3.7.6-linux64-20200216T2303.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200216/cpython-3.7.6-macos-20200216T2344.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200216/cpython-3.7.6-windows-amd64-shared-pgo-20200217T0022.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20200216/cpython-3.7.6-windows-x86-shared-pgo-20200217T0110.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 7, 5, False): {
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20191025/cpython-3.7.5-linux64-20191025T0506.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20191025/cpython-3.7.5-macos-20191026T0535.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20191025/cpython-3.7.5-windows-amd64-20191025T0540.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20191025/cpython-3.7.5-windows-x86-20191025T0549.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 7, 4, False): {
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20190816/cpython-3.7.4-linux64-20190817T0224.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20190816/cpython-3.7.4-macos-20190817T0220.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20190816/cpython-3.7.4-windows-amd64-20190817T0227.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20190816/cpython-3.7.4-windows-x86-20190817T0235.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 7, 3, False): {
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20190617/cpython-3.7.3-linux64-20190618T0324.tar.zst",
            None,
        ),
        ("macos", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20190617/cpython-3.7.3-macos-20190618T0523.tar.zst",
            None,
        ),
        ("windows", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20190617/cpython-3.7.3-windows-amd64-20190618T0516.tar.zst",
            None,
        ),
        ("windows", "x86", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20190617/cpython-3.7.3-windows-x86-20190709T0348.tar.zst",
            None,
        ),
    },
    PythonVersion("cpython", 3, 7, 1, False): {
        ("linux", "x86_64", False): (
            "https://github.com/astral-sh/python-build-standalone/releases/download/20181218/cpython-3.7.1-linux64-20181218T1905.tar.zst",
            None,
        )
    },
    PythonVersion("pypy", 3, 11, 13, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.20-aarch64.tar.bz2",
            "9347fe691a07fd9df17a1b186554fb9d9e6210178ffef19520a579ce1f9eb741",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.20-linux32.tar.bz2",
            "d08ce15dd61e9ace5e010b047104f0137110a258184e448ea8239472f10cf99b",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.20-linux64.tar.bz2",
            "1410db3a7ae47603e2b7cbfd7ff6390b891b2e041c9eb4f1599f333677bccb3e",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.20-macos_x86_64.tar.bz2",
            "bb3ae80cf5fca5044af2e42933e7692c7c5e76a828ce0eb6404a5d5da83b313c",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.20-macos_arm64.tar.bz2",
            "84a48e09c97f57df62cc9f01b7a6d8c3e306b6270671d871aa8ab8c06945940d",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.20-win64.zip",
            "a8d36f6ceb1d9be6cf24a73b0ba103e7567e396b2f7a33426b05e4a06330755b",
        ),
    },
    PythonVersion("pypy", 3, 11, 11, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.19-aarch64.tar.bz2",
            "13207dbf81ce24e96da760b1b863627b77bb20b1fb4c95191e02a0b72383df74",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.19-linux32.tar.bz2",
            "5c6cdafd0a0abd14ca59926ed1b6aeb13b228c18b4b46de655aae48734c731ad",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.19-linux64.tar.bz2",
            "9177d9e0bb91b05f921c642cb0ff71a0f3653b5d29a42d40d6a078c15b75720f",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.19-macos_x86_64.tar.bz2",
            "a2439f9d30dfdae96a5e9101c7dc54a8a68b56c9d7314ea399b0a25d3e87ebb2",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.19-macos_arm64.tar.bz2",
            "7704e0d5302e53920d32dcfe9afeeb10436d4c94233e8830cf603aa955a861c1",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.11-v7.3.19-win64.zip",
            "b61c7c1dbf879eda6f779c374bfbbeecd3f618ada08404705a1a19d39df48dbd",
        ),
    },
    PythonVersion("pypy", 3, 10, 16, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.19-aarch64.tar.bz2",
            "af27a589178f11198e2244ab65ca510630ba97c131d7ccc4021eb5bc58de7f57",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.19-linux32.tar.bz2",
            "e63a4fcad2641ee541e852918befb513abf04ce7070f743a50778cae9f9da80e",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.19-linux64.tar.bz2",
            "c73ac2cc2380ac9227fd297482bf2a3e17a80618ba46db7544d535515321ec1e",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.19-macos_x86_64.tar.bz2",
            "2c5e5c2a33ac882551d7f28b98d19d486b8995aa50824a73b4edcc6aeec35c63",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.19-macos_arm64.tar.bz2",
            "3db8a03fc496164801646844d7f3b12baa0adb3ad9a0b7cb124521bc2e168a36",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.19-win64.zip",
            "c0d07bba6c8fb4e5804f4a8b3f8ef07cc3d89f6ad1db42a45ffb9be60bbb7cc2",
        ),
    },
    PythonVersion("pypy", 3, 10, 14, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.17-aarch64.tar.bz2",
            "53b6e5907df869c49e4eae7aca09fba16d150741097efb245892c1477d2395f2",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.17-linux32.tar.bz2",
            "e534110e1047da37c1d586c392f74de3424f871d906a2083de6d41f2a8cc9164",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.17-linux64.tar.bz2",
            "fdcdb9b24f1a7726003586503fdeb264fd68fc37fbfcea022dcfe825a7fee18b",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.17-macos_x86_64.tar.bz2",
            "6c2c5f2300d7564e711421b4968abd63243cb96f76e363975dd648ebf4a362ee",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.17-macos_arm64.tar.bz2",
            "a050e25e8d686853dd5afc363e55625165825dacfb55f8753d8225ebe417cfd2",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.17-win64.zip",
            "cab794a03ddda26238c72942ea6f225612e0dc17c76cac6652da83a95024e6e8",
        ),
    },
    PythonVersion("pypy", 3, 10, 13, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.15-aarch64.tar.bz2",
            "52146fccaf64e87e71d178dda8de63c01577ec3923073dc69e1519622bcacb74",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.15-linux32.tar.bz2",
            "75dd58c9abd8b9d78220373148355bc3119febcf27a2c781d64ad85e7232c4aa",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.15-linux64.tar.bz2",
            "33c584e9a70a71afd0cb7dd8ba9996720b911b3b8ed0156aea298d4487ad22c3",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.15-macos_x86_64.tar.bz2",
            "559b61ba7e7c5a5c23cef5370f1fab47ccdb939ac5d2b42b4bef091abe3f6964",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.15-macos_arm64.tar.bz2",
            "d927c5105ea7880f7596fe459183e35cc17c853ef5105678b2ad62a8d000a548",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.15-win64.zip",
            "b378b3ab1c3719aee0c3e5519e7bff93ff67b2d8aa987fe4f088b54382db676c",
        ),
    },
    PythonVersion("pypy", 3, 10, 12, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.12-aarch64.tar.bz2",
            "26208b5a134d9860a08f74cce60960005758e82dc5f0e3566a48ed863a1f16a1",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.12-linux32.tar.bz2",
            "811667825ae58ada4b7c3d8bc1b5055b9f9d6a377e51aedfbe0727966603f60e",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.12-linux64.tar.bz2",
            "6c577993160b6f5ee8cab73cd1a807affcefafe2f7441c87bd926c10505e8731",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.12-macos_x86_64.tar.bz2",
            "dbc15d8570560d5f79366883c24bc42231a92855ac19a0f28cb0adeb11242666",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.12-macos_arm64.tar.bz2",
            "45671b1e9437f95ccd790af10dbeb57733cca1ed9661463b727d3c4f5caa7ba0",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.10-v7.3.12-win64.zip",
            "8c3b1d34fb99100e230e94560410a38d450dc844effbee9ea183518e4aff595c",
        ),
    },
    PythonVersion("pypy", 3, 9, 19, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.16-aarch64.tar.bz2",
            "de3f2ed3581b30555ac0dd3e4df78a262ec736a36fb2e8f28259f8539b278ef4",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.16-linux32.tar.bz2",
            "583b6d6dd4e8c07cbc04da04a7ec2bdfa6674825289c2378c5e018d5abe779ea",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.16-linux64.tar.bz2",
            "16f9c5b808c848516e742986e826b833cdbeda09ad8764e8704595adbe791b23",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.16-macos_x86_64.tar.bz2",
            "fda015431621e7e5aa16359d114f2c45a77ed936992c1efff86302e768a6b21c",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.16-macos_arm64.tar.bz2",
            "88f824e7a2d676440d09bc90fc959ae0fd3557d7e2f14bfbbe53d41d159a47fe",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.16-win64.zip",
            "06ec12a5e964dc0ad33e6f380185a4d295178dce6d6df512f508e7aee00a1323",
        ),
    },
    PythonVersion("pypy", 3, 9, 18, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.15-aarch64.tar.bz2",
            "03e35fcba290454bb0ccf7ee57fb42d1e63108d10d593776a382c0a2fe355de0",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.15-linux32.tar.bz2",
            "c6209380977066c9e8b96e8258821c70f996004ce1bc8659ae83d4fd5a89ff5c",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.15-linux64.tar.bz2",
            "f062be307200bde434817e1620cebc13f563d6ab25309442c5f4d0f0d68f0912",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.15-macos_x86_64.tar.bz2",
            "18ad7c9cb91c5e8ef9d40442b2fd1f6392ae113794c5b6b7d3a45e04f19edec6",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.15-macos_arm64.tar.bz2",
            "300541c32125767a91b182b03d9cc4257f04971af32d747ecd4d62549d72acfd",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.15-win64.zip",
            "a156dad8b58570597eaaabe05663f00f80c60bc11df4a9c46d0953b6c5eb9209",
        ),
    },
    PythonVersion("pypy", 3, 9, 17, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.12-aarch64.tar.bz2",
            "e9327fb9edaf2ad91935d5b8563ec5ff24193bddb175c1acaaf772c025af1824",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.12-linux32.tar.bz2",
            "aa04370d38f451683ccc817d76c2b3e0f471dbb879e0bd618d9affbdc9cd37a4",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.12-linux64.tar.bz2",
            "84c89b966fab2b58f451a482ee30ca7fec3350435bd0b9614615c61dc6da2390",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.12-macos_x86_64.tar.bz2",
            "64f008ffa070c407e5ef46c8256b2e014de7196ea5d858385861254e7959f4eb",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.12-macos_arm64.tar.bz2",
            "0e8a1a3468b9790c734ac698f5b00cc03fc16899ccc6ce876465fac0b83980e3",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.12-win64.zip",
            "0996054207b401aeacace1aa11bad82cfcb463838a1603c5f263626c47bbe0e6",
        ),
    },
    PythonVersion("pypy", 3, 9, 16, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.11-aarch64.tar.bz2",
            "09175dc652ed895d98e9ad63d216812bf3ee7e398d900a9bf9eb2906ba8302b9",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.11-linux32.tar.bz2",
            "0099d72c2897b229057bff7e2c343624aeabdc60d6fb43ca882bff082f1ffa48",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.11-linux64.tar.bz2",
            "d506172ca11071274175d74e9c581c3166432d0179b036470e3b9e8d20eae581",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.11-macos_x86_64.tar.bz2",
            "d33f40b207099872585afd71873575ca6ea638a27d823bc621238c5ae82542ed",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.11-macos_arm64.tar.bz2",
            "91ad7500f1a39531dbefa0b345a3dcff927ff9971654e8d2e9ef7c5ae311f57e",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.11-win64.zip",
            "57faad132d42d3e7a6406fcffafffe0b4f390cf0e2966abb8090d073c6edf405",
        ),
    },
    PythonVersion("pypy", 3, 9, 15, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.10-aarch64.tar.bz2",
            "657a04fd9a5a992a2f116a9e7e9132ea0c578721f59139c9fb2083775f71e514",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.10-linux32.tar.bz2",
            "b6db59613b9a1c0c1ab87bc103f52ee95193423882dc8a848b68850b8ba59cc5",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.10-linux64.tar.bz2",
            "95cf99406179460d63ddbfe1ec870f889d05f7767ce81cef14b88a3a9e127266",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.10-macos_x86_64.tar.bz2",
            "f90c8619b41e68ec9ffd7d5e913fe02e60843da43d3735b1c1bc75bcfe638d97",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.10-macos_arm64.tar.bz2",
            "e2a6bec7408e6497c7de8165aa4a1b15e2416aec4a72f2578f793fb06859ccba",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.10-win64.zip",
            "07e18b7b24c74af9730dfaab16e24b22ef94ea9a4b64cbb2c0d80610a381192a",
        ),
    },
    PythonVersion("pypy", 3, 9, 12, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.9-aarch64.tar.bz2",
            "2e1ae193d98bc51439642a7618d521ea019f45b8fb226940f7e334c548d2b4b9",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.9-linux32.tar.bz2",
            "0de4b9501cf28524cdedcff5052deee9ea4630176a512bdc408edfa30914bae7",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.9-linux64.tar.bz2",
            "46818cb3d74b96b34787548343d266e2562b531ddbaf330383ba930ff1930ed5",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.9-osx64.tar.bz2",
            "59c8852168b2b1ba1f0211ff043c678760380d2f9faf2f95042a8878554dbc25",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.9-win64.zip",
            "be48ab42f95c402543a7042c999c9433b17e55477c847612c8733a583ca6dff5",
        ),
    },
    PythonVersion("pypy", 3, 9, 10, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.8-aarch64-portable.tar.bz2",
            "b7282bc4484bceae5bc4cc04e05ee4faf51cb624c8fc7a69d92e5fdf0d0c96aa",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.8-linux32.tar.bz2",
            "a0d18e4e73cc655eb02354759178b8fb161d3e53b64297d05e2fff91f7cf862d",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.8-linux64.tar.bz2",
            "129a055032bba700cd1d0acacab3659cf6b7180e25b1b2f730e792f06d5b3010",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.8-osx64.tar.bz2",
            "95bd88ac8d6372cd5b7b5393de7b7d5c615a0c6e42fdb1eb67f2d2d510965aee",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.9-v7.3.8-win64.zip",
            "c1b2e4cde2dcd1208d41ef7b7df8e5c90564a521e7a5db431673da335a1ba697",
        ),
    },
    PythonVersion("pypy", 3, 8, 16, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.11-aarch64.tar.bz2",
            "9a2fa0b8d92b7830aa31774a9a76129b0ff81afbd22cd5c41fbdd9119e859f55",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.11-linux32.tar.bz2",
            "a79b31fce8f5bc1f9940b6777134189a1d3d18bda4b1c830384cda90077c9176",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.11-linux64.tar.bz2",
            "470330e58ac105c094041aa07bb05676b06292bc61409e26f5c5593ebb2292d9",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.11-macos_x86_64.tar.bz2",
            "194ca0b4d91ae409a9cb1a59eb7572d7affa8a451ea3daf26539aa515443433a",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.11-macos_arm64.tar.bz2",
            "78cdc79ff964c4bfd13eb45a7d43a011cbe8d8b513323d204891f703fdc4fa1a",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.11-win64.zip",
            "0f46fb6df32941ea016f77cfd7e9b426d5ac25a2af2453414df66103941c8435",
        ),
    },
    PythonVersion("pypy", 3, 8, 15, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.10-aarch64.tar.bz2",
            "e4caa1a545f22cfee87d5b9aa6f8852347f223643ad7d2562e0b2a2f4663ad98",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.10-linux32.tar.bz2",
            "b70ed7fdc73a74ebdc04f07439f7bad1a849aaca95e26b4a74049d0e483f071c",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.10-linux64.tar.bz2",
            "ceef6496fd4ab1c99e3ec22ce657b8f10f8bb77a32427fadfb5e1dd943806011",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.10-macos_x86_64.tar.bz2",
            "399eb1ce4c65f62f6a096b7c273536601b7695e3c0dc0457393a659b95b7615b",
        ),
        ("macos", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.10-macos_arm64.tar.bz2",
            "6cb1429371e4854b718148a509d80143f801e3abfc72fef58d88aeeee1e98f9e",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.10-win64.zip",
            "362dd624d95bd64743190ea2539b97452ecb3d53ea92ceb2fbe9f48dc60e6b8f",
        ),
    },
    PythonVersion("pypy", 3, 8, 13, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.9-aarch64.tar.bz2",
            "5e124455e207425e80731dff317f0432fa0aba1f025845ffca813770e2447e32",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.9-linux32.tar.bz2",
            "4b261516c6c59078ab0c8bd7207327a1b97057b4ec1714ed5e79a026f9efd492",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.9-linux64.tar.bz2",
            "08be25ec82fc5d23b78563eda144923517daba481a90af0ace7a047c9c9a3c34",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.9-osx64.tar.bz2",
            "91a5c2c1facd5a4931a8682b7d792f7cf4f2ba25cd2e7e44e982139a6d5e4840",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.9-win64.zip",
            "05022baaa55db2b60880f2422312d9e4025e1267303ac57f33e8253559d0be88",
        ),
    },
    PythonVersion("pypy", 3, 8, 12, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.8-aarch64-portable.tar.bz2",
            "0210536e9f1841ba283c13b04783394050837bb3e6f4091c9f1bd9c7f2b94b55",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.8-linux32.tar.bz2",
            "bea4b275decd492af6462157d293dd6fcf08a949859f8aec0959537b40afd032",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.8-linux64.tar.bz2",
            "089f8e3e357d6130815964ddd3507c13bd53e4976ccf0a89b5c36a9a6775a188",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.8-osx64.tar.bz2",
            "de1b283ff112d76395c0162a1cf11528e192bdc230ee3f1b237f7694c7518dee",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.8-v7.3.8-win64.zip",
            "0894c468e7de758c509a602a28ef0ba4fbf197ccdf946c7853a7283d9bb2a345",
        ),
    },
    PythonVersion("pypy", 3, 7, 13, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.9-aarch64.tar.bz2",
            "dfc62f2c453fb851d10a1879c6e75c31ffebbf2a44d181bb06fcac4750d023fc",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.9-linux32.tar.bz2",
            "3398cece0167b81baa219c9cd54a549443d8c0a6b553ec8ec13236281e0d86cd",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.9-linux64.tar.bz2",
            "c58195124d807ecc527499ee19bc511ed753f4f2e418203ca51bc7e3b124d5d1",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.9-osx64.tar.bz2",
            "12d92f578a200d50959e55074b20f29f93c538943e9a6e6522df1a1cc9cef542",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.9-win64.zip",
            "8acb184b48fb3c854de0662e4d23a66b90e73b1ab73a86695022c12c745d8b00",
        ),
    },
    PythonVersion("pypy", 3, 7, 12, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.8-aarch64-portable.tar.bz2",
            "639c76f128a856747aee23a34276fa101a7a157ea81e76394fbaf80b97dcf2f2",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.8-linux32.tar.bz2",
            "38429ec6ea1aca391821ee4fbda7358ae86de4600146643f2af2fe2c085af839",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.8-linux64.tar.bz2",
            "409085db79a6d90bfcf4f576dca1538498e65937acfbe03bd4909bdc262ff378",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.8-osx64.tar.bz2",
            "76b8eef5b059a7e478f525615482d2a6e9feb83375e3f63c16381d80521a693f",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.8-win64.zip",
            "96df67492bc8d62b2e71dddf5f6c58965a26cac9799c5f4081401af0494b3bcc",
        ),
    },
    PythonVersion("pypy", 3, 7, 10, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.5-aarch64.tar.bz2",
            "85d83093b3ef5b863f641bc4073d057cc98bb821e16aa9361a5ff4898e70e8ee",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.5-linux32.tar.bz2",
            "3dd8b565203d372829e53945c599296fa961895130342ea13791b17c84ed06c4",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.5-linux64.tar.bz2",
            "9000db3e87b54638e55177e68cbeb30a30fe5d17b6be48a9eb43d65b3ebcfc26",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.5-osx64.tar.bz2",
            "b3a7d3099ad83de7c267bb79ae609d5ce73b01800578ffd91ba7e221b13f80db",
        ),
        ("windows", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.5-win64.zip",
            "072bd22427178dc4e65d961f50281bd2f56e11c4e4d9f16311c703f69f46ae24",
        ),
    },
    PythonVersion("pypy", 3, 7, 9, False): {
        ("linux", "aarch64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.3-aarch64.tar.bz2",
            "ee4aa041558b58de6063dd6df93b3def221c4ca4c900d6a9db5b1b52135703a8",
        ),
        ("linux", "x86", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.3-linux32.tar.bz2",
            "7d81b8e9fcd07c067cfe2f519ab770ec62928ee8787f952cadf2d2786246efc8",
        ),
        ("linux", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.3-linux64.tar.bz2",
            "37e2804c4661c86c857d709d28c7de716b000d31e89766599fdf5a98928b7096",
        ),
        ("macos", "x86_64", True): (
            "https://downloads.python.org/pypy/pypy3.7-v7.3.3-osx64.tar.bz2",
            "d72b27d5bb60813273f14f07378a08822186a66e216c5d1a768ad295b582438d",
        ),
    },
}
