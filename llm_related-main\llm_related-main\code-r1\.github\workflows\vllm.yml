# # Tests layout

# Each folder under tests/ corresponds to a test category for a sub-namespace in verl. For instance:
# - `tests/trainer` for testing functionality related to `verl/trainer`
# - `tests/models` for testing functionality related to `verl/models`
# - ...

# There are a few folders with `special_` prefix, created for special purposes:
# - `special_distributed`: unit tests that must run with multiple GPUs
# - `special_e2e`: end-to-end tests with training/generation scripts
# - `special_npu`: tests for NPUs
# - `special_sanity`: a suite of quick sanity tests
# - `special_standalone`: a set of test that are designed to run in dedicated environments

# Accelerators for tests
# - By default tests are run with GPU available, except for the ones under `special_npu`, and any test script whose name ends with `on_cpu.py`.
# - For test scripts with `on_cpu.py` name suffix would be tested on CPU resources in linux environment.

# # Workflow layout

# All CI tests are configured by yaml files in `.github/workflows/`. Here's an overview of all test configs:
# 1. A list of always triggered CPU sanity tests: `check-pr-title.yml`, `secrets_scan.yml`, `check-pr-title,yml`, `pre-commit.yml`, `doc.yml`
# 2. Some heavy multi-GPU unit tests, such as `model.yml`, `vllm.yml`, `sgl.yml`
# 3. End-to-end tests: `e2e_*.yml`
# 4. Unit tests
#   - `cpu_unit_tests.yml`, run pytest on all scripts with file name pattern `tests/**/test_*_on_cpu.py`
#   - `gpu_unit_tests.yml`, run pytest on all scripts with file without the `on_cpu.py` suffix.
#   - Since cpu/gpu unit tests by default runs all tests under `tests`, please make sure tests are manually excluded in them when
#     - new workflow yaml is added to `.github/workflows`
#     - new tests are added to workflow mentioned in 2.

name: vllm

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.*
  pull_request:
    branches:
      - main
      - v0.*
    paths:
      - "**/*.py"
      # Other entrypoints
      - "!examples/**"
      - "!tests/**"
      - "!verl/trainer/main_*.py"
      - "!verl/trainer/fsdp_sft_trainer.py"
      # Recipes
      - "!recipe/**"
      # FSDP
      - "!verl/workers/**/*dp_*.py"
      # Megatron
      - "!verl/workers/**/megatron_*.py"
      # SGLang
      - "!**/*sglang*"
      # Entrypoints
      - ".github/workflows/vllm.yml"
      - "tests/special_e2e/generation"
      - "tests/workers/rollout"
      - "verl/trainer/main_generation.py"
      - "verl/trainer/config/generation.yaml"

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions:
  contents: read

jobs:
  vllm:
    runs-on: [L20x8]
    timeout-minutes: 60 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1,hf-mirror.com"
      HF_ENDPOINT: "https://hf-mirror.com"
      HF_HUB_ENABLE_HF_TRANSFER: "0" # This is more stable
    container:
      image: whatcanyousee/verl:vemlp-th2.4.0-cu124-vllm0.6.3-ray2.10-te2.0-megatron0.11.0-v0.0.6
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install -e .[test,vllm]
          pip install tensordict==0.6.2
      - name: Download Model to Use
        run: |
          huggingface-cli download Qwen/Qwen2.5-0.5B-Instruct
          huggingface-cli download Qwen/Qwen2.5-1.5B-Instruct
          huggingface-cli download 'Qwen/Qwen2-7B-Instruct'
          huggingface-cli download 'deepseek-ai/deepseek-llm-7b-chat'
          export HF_HUB_OFFLINE=1
        # Disable requests to avoid network errors
      - name: Test the latest vLLM
        run: |
          cd tests/workers/rollout/rollout_vllm
          torchrun --standalone --nnodes=1 --nproc_per_node=4 $(which pytest) -s test_vllm_spmd.py
      - name: Run Qwen 0.5B generation test
        run: |
          cd tests/special_e2e/generation
          export OUTPUT_PATH="${HOME}/data/gen/qwen_05_gen_test.parquet"
          MODEL_ID=Qwen/Qwen2.5-0.5B-Instruct NGPUS_PER_NODE=4 GEN_TP=2 bash ./run_gen_qwen05.sh
          rm -rf "${OUTPUT_PATH}"
      - name: Run Qwen 0.5B generation test when world_size == 1
        run: |
          cd tests/special_e2e/generation
          export OUTPUT_PATH="${HOME}/data/gen/qwen_05_gen_test.parquet"
          MODEL_ID=Qwen/Qwen2.5-0.5B-Instruct NGPUS_PER_NODE=1 GEN_TP=1 bash ./run_gen_qwen05.sh
          rm -rf "${OUTPUT_PATH}"
      - name: Test the latest vLLM Rollout async with agent loop
        run: |
          ROLLOUT_NAME=vllm pytest -svvv tests/experimental/agent_loop/test_basic_agent_loop.py
      # Note(haibin.lin): for any new test, please update gpu_unit_tests.yaml to avoid repeated tests
