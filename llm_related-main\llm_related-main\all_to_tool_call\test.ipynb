{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatCompletion(id='0196bec84e9e399c781eb23b425fd788', choices=[Choice(finish_reason='tool_calls', index=0, logprobs=None, message=ChatCompletionMessage(content='', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='0196bec8536338780ca82d04571819e9', function=Function(arguments='{\"location\": \"北京市\"}', name='get_current_weather'), type='function')]))], created=1746957520, model='Qwen/Qwen2.5-7B-Instruct', object='chat.completion', service_tier=None, system_fingerprint='', usage=CompletionUsage(completion_tokens=22, prompt_tokens=273, total_tokens=295, completion_tokens_details=None, prompt_tokens_details=None))"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from openai import OpenAI\n", "from datetime import datetime\n", "import json\n", "import os\n", "import random\n", "\n", "# client = OpenAI(\n", "#     api_key='sk-zfaguzfmjrruybpjgwaxabwytcdgwrvrcsldmxigrsmolpyt',\n", "#     base_url=\"https://api.siliconflow.cn/v1\",\n", "# )\n", "\n", "client = OpenAI(\n", "    api_key='yyy',\n", "    base_url=\"http://***********:8888/v1\",\n", ")\n", "\n", "tools = [\n", "    # 工具1 获取当前时刻的时间\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"get_current_time\",\n", "            \"description\": \"当你想知道现在的时间时非常有用。\",\n", "            \"parameters\": {},\n", "        },\n", "    },\n", "    # 工具2 获取指定城市的天气\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"get_current_weather\",\n", "            \"description\": \"当你想查询指定城市的天气时非常有用。\",\n", "            \"parameters\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    # 查询天气时需要提供位置，因此参数设置为location\n", "                    \"location\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"城市或县区，比如北京市、杭州市、余杭区等。\",\n", "                    }\n", "                },\n", "                \"required\": [\"location\"],\n", "            },\n", "        },\n", "    },\n", "]\n", "\n", "\n", "# 封装模型响应函数\n", "def get_response(query):\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"You are a helpful assistant.\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": query\n", "        }\n", "    ]\n", "    completion = client.chat.completions.create(\n", "        model=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n", "        messages=messages,\n", "        tools=tools,\n", "    )\n", "    return completion\n", "\n", "get_response('北京天气怎么样')"]}], "metadata": {"kernelspec": {"display_name": "wyf", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}