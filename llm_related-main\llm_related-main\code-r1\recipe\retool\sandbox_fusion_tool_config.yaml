tools:
  - class_name: "recipe.retool.retool.CustomSandboxFusionTool"
    config:
      sandbox_fusion_url: "https://***.apigateway-cn-beijing.volceapi.com/run_code"
      num_workers: 128
      enable_global_rate_limit: true
      rate_limit: 128
      default_timeout: 30
      default_language: "python"
      memory_limit_mb: 1024
      type: native

    tool_schema:
      type: "function"
      function:
        name: "code_interpreter"
        description: "A tool for executing code."
        parameters:
          type: "object"
          properties:
            code:
              type: "string"
              description: "The code to execute."
          required: ["code"]
