Metadata-Version: 2.1
Name: findpython
Version: 0.6.3
Summary: A utility to find python versions on your system
Author-Email: <PERSON> <<EMAIL>>
License: MIT
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Project-URL: Homepage, https://github.com/frostming/findpython
Requires-Python: >=3.8
Requires-Dist: packaging>=20
Description-Content-Type: text/markdown

# FindPython

_A utility to find python versions on your system._

[![Tests](https://github.com/frostming/findpython/actions/workflows/ci.yml/badge.svg)](https://github.com/frostming/findpython/actions/workflows/ci.yml)
[![PyPI](https://img.shields.io/pypi/v/findpython?logo=python&logoColor=%23cccccc&style=flat-square)](https://pypi.org/project/findpython)
[![PyPI - Python Version](https://img.shields.io/pypi/pyversions/findpython?logo=python&logoColor=%23cccccc&style=flat-square)](https://pypi.org/project/findpython)
[![pdm-managed](https://img.shields.io/badge/pdm-managed-blueviolet?style=flat-square)](https://github.com/frostming/findpython)

## Description

This library is a rewrite of [pythonfinder] project by [@techalchemy][techalchemy].
It simplifies the whole code structure while preserving most of the original features.

[pythonfinder]: https://github.com/sarugaku/pythonfinder
[techalchemy]: https://github.com/techalchemy

## Installation

FindPython is installable via any kind of package manager including `pip`:

```bash
pip install findpython
```

<details>
<summary>Expand this section to see findpython's availability in the package ecosystem</summary>

<a href="https://repology.org/project/python:findpython/versions">
    <img src="https://repology.org/badge/vertical-allrepos/python:findpython.svg?header=python%3Afindpython" alt="Packaging status">
</a>
</details>

## Usage

```python
>>> import findpython
>>> findpython.find(3, 9)  # Find by major and minor version
<PythonVersion executable=PosixPath('/opt/homebrew/bin/python3.9'), version=<Version('3.9.10')>, architecture='64bit', major=3, minor=9, patch=10>
>>> findpython.find("3.9")  # Find by version string
<PythonVersion executable=PosixPath('/opt/homebrew/bin/python3.9'), version=<Version('3.9.10')>, architecture='64bit', major=3, minor=9, patch=10>
>>> findpython.find("3.9-32")  # Find by version string and architecture
<PythonVersion executable=WindowsPath('C:\\Python\\3.9-32\\python.exe'), version=<Version('3.9.10')>, architecture='32bit', major=3, minor=9, patch=10>
>>> findpython.find(name="python3")  # Find by executable name
<PythonVersion executable=PosixPath('/Users/<USER>/Library/PythonUp/bin/python3'), version=<Version('3.10.2')>, architecture='64bit', major=3, minor=10, patch=2>
>>> findpython.find("python3")  # Find by executable name without keyword argument, same as above
<PythonVersion executable=PosixPath('/Users/<USER>/Library/PythonUp/bin/python3'), version=<Version('3.10.2')>, architecture='64bit', major=3, minor=10, patch=2>
>>> findpython.find_all(major=3, minor=9)  # Same arguments as `find()`, but return all matches
[<PythonVersion executable=PosixPath('/opt/homebrew/bin/python3.9'), version=<Version('3.9.10')>, architecture='64bit', major=3, minor=9, patch=10>, <PythonVersion executable=PosixPath('/opt/homebrew/bin/python3'), version=<Version('3.9.10')>, architecture='64bit', major=3, minor=9, patch=10>, <PythonVersion executable=PosixPath('/Users/<USER>/Library/PythonUp/cmd/python3.9'), version=<Version('3.9.9')>, architecture='64bit', major=3, minor=9, patch=9>, <PythonVersion executable=PosixPath('/usr/local/bin/python3.9'), version=<Version('3.9.5')>, architecture='64bit', major=3, minor=9, patch=5>, <PythonVersion executable=PosixPath('/usr/local/bin/python3'), version=<Version('3.9.5')>, architecture='64bit', major=3, minor=9, patch=5>]
```

## CLI Usage

In addition, FindPython provides a CLI interface to find python versions:

```
usage: findpython [-h] [-V] [-a] [--resolve-symlink] [-v] [--no-same-file] [--no-same-python] [--providers PROVIDERS]
                  [version_spec]

A utility to find python versions on your system

positional arguments:
  version_spec          Python version spec or name

options:
  -h, --help            show this help message and exit
  -V, --version         show program's version number and exit
  -a, --all             Show all matching python versions
  --resolve-symlink     Resolve all symlinks
  -v, --verbose         Verbose output
  --no-same-file        Eliminate the duplicated results with the same file contents
  --no-same-python      Eliminate the duplicated results with the same sys.executable
  --providers PROVIDERS
                        Select provider(s) to use
```

## Integration

FindPython finds Python from the following places:

-   `PATH` environment variable
-   pyenv install root
-   asdf python install root
-   [rye](https://rye-up.com) toolchain install root
-   `/Library/Frameworks/Python.framework/Versions` (MacOS)
-   Windows registry (Windows only)

## License

FindPython is released under MIT License.
