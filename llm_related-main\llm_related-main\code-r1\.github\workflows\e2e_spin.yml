name: e2e_spin

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.*
    paths:
      - "**/*.py"
      # Other entrypoints
      - "!examples/**"
      - "!tests/**"
      - "!verl/trainer/main_*.py"
      - "!verl/trainer/fsdp_sft_trainer.py"
      # Other recipes
      - "!recipe/**"
      # Megatron
      - "!verl/workers/**/megatron_*.py"
      # Home
      - "recipe/spin"
      # Entrypoints
      - ".github/workflows/e2e_spin.yml"
      - "examples/data_preprocess/gsm8k.py"
      - "tests/special_e2e/run_spin.sh"
      - "!examples"
  pull_request:
    branches:
      - main
      - v0.*
    paths:
      - "**/*.py"
      # Other entrypoints
      - "!examples/**"
      - "!tests/**"
      - "!verl/trainer/main_*.py"
      - "!verl/trainer/fsdp_sft_trainer.py"
      # Other recipes
      - "!recipe/**"
      # Megatron
      - "!verl/workers/**/megatron_*.py"
      # Home
      - "recipe/spin"
      # Entrypoints
      - ".github/workflows/e2e_spin.yml"
      - "examples/data_preprocess/gsm8k.py"
      - "tests/special_e2e/run_spin.sh"
      - "!examples"

# Declare permissions just read content.
permissions:
  contents: read

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

jobs:
  e2e_spin:
    runs-on: [L20x8]
    timeout-minutes: 40 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1,hf-mirror.com"
      HF_ENDPOINT: "https://hf-mirror.com"
      HF_HUB_ENABLE_HF_TRANSFER: "0" # This is more stable
    container:
      image: verlai/verl:app-verl0.4-sglang0.4.6.post5-vllm0.8.5-mcore0.12.1
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install -e .[test,gpu,sglang] --no-deps
      - name: Prepare gsm8k dataset
        run: |
          python3 examples/data_preprocess/gsm8k.py --local_dir ./data/gsm8k
      - name: Prepare Model checkpoint
        run: |
          huggingface-cli download Qwen/Qwen2.5-0.5B-Instruct --local-dir ./models/Qwen2.5-0.5B-Instruct
      - name: Running the E2E test with the spin algorithm
        run: |
          ray stop --force
          bash tests/special_e2e/run_spin.sh
